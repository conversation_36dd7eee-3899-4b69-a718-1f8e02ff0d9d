a,
abbr,
acronym,
address,
applet,
article,
aside,
audio,
b,
big,
blockquote,
body,
button,
canvas,
caption,
center,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
input,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
output,
p,
pre,
q,
ruby,
s,
samp,
section,
select,
small,
span,
strike,
strong,
sub,
summary,
sup,
table,
tbody,
td,
textarea,
tfoot,
th,
thead,
time,
tr,
tt,
u,
ul,
var,
video {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

body {
    line-height: 1;
}

a {
    text-decoration: none;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote:after,
blockquote:before,
q:after,
q:before {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

.ladi-loading {
    z-index: 900000000000;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.1);
}

.ladi-loading .loading {
    width: 80px;
    height: 80px;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    overflow: hidden;
    position: absolute;
}

.ladi-loading .loading div {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #fff;
    border-radius: 50%;
    animation: ladi-loading 1.2s linear infinite;
}

.ladi-loading .loading div:nth-child(1) {
    animation-delay: 0s;
    top: 37px;
    left: 66px;
}

.ladi-loading .loading div:nth-child(2) {
    animation-delay: -0.1s;
    top: 22px;
    left: 62px;
}

.ladi-loading .loading div:nth-child(3) {
    animation-delay: -0.2s;
    top: 11px;
    left: 52px;
}

.ladi-loading .loading div:nth-child(4) {
    animation-delay: -0.3s;
    top: 7px;
    left: 37px;
}

.ladi-loading .loading div:nth-child(5) {
    animation-delay: -0.4s;
    top: 11px;
    left: 22px;
}

.ladi-loading .loading div:nth-child(6) {
    animation-delay: -0.5s;
    top: 22px;
    left: 11px;
}

.ladi-loading .loading div:nth-child(7) {
    animation-delay: -0.6s;
    top: 37px;
    left: 7px;
}

.ladi-loading .loading div:nth-child(8) {
    animation-delay: -0.7s;
    top: 52px;
    left: 11px;
}

.ladi-loading .loading div:nth-child(9) {
    animation-delay: -0.8s;
    top: 62px;
    left: 22px;
}

.ladi-loading .loading div:nth-child(10) {
    animation-delay: -0.9s;
    top: 66px;
    left: 37px;
}

.ladi-loading .loading div:nth-child(11) {
    animation-delay: -1s;
    top: 62px;
    left: 52px;
}

.ladi-loading .loading div:nth-child(12) {
    animation-delay: -1.1s;
    top: 52px;
    left: 62px;
}

@keyframes ladi-loading {
    0%,
    100%,
    20%,
    80% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.5);
    }
}

.ladipage-message {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 10000000000;
    background: rgba(0, 0, 0, 0.3);
}

.ladipage-message .ladipage-message-box {
    width: 400px;
    max-width: calc(100% - 50px);
    height: 160px;
    border: 1px solid rgba(0, 0, 0, 0.3);
    background-color: #fff;
    position: fixed;
    top: calc(50% - 155px);
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 10px;
}

.ladipage-message .ladipage-message-box span {
    display: block;
    background-color: rgba(6, 21, 40, 0.05);
    color: #000;
    padding: 12px 15px;
    font-weight: 600;
    font-size: 16px;
    line-height: 16px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.ladipage-message .ladipage-message-box .ladipage-message-text {
    display: -webkit-box;
    font-size: 14px;
    padding: 0 20px;
    margin-top: 16px;
    line-height: 20px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}

.ladipage-message .ladipage-message-box .ladipage-message-close {
    display: block;
    position: absolute;
    right: 15px;
    bottom: 10px;
    margin: 0 auto;
    padding: 10px 0;
    border: none;
    width: 80px;
    text-transform: uppercase;
    text-align: center;
    color: #000;
    background-color: #e6e6e6;
    border-radius: 5px;
    text-decoration: none;
    font-size: 14px;
    line-height: 14px;
    font-weight: 600;
    cursor: pointer;
    outline: 0;
}

.lightbox-screen {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    z-index: 9000000080;
    background: rgba(0, 0, 0, 0.5);
}

.lightbox-screen .lightbox-close {
    position: absolute;
    z-index: 9000000090;
    cursor: pointer;
}

.lightbox-screen .lightbox-hidden {
    display: none;
}

.lightbox-screen .lightbox-close {
    width: 16px;
    height: 16px;
    margin: 10px;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url('data:image/svg+xml;utf8, %3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%23fff%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M23.4144%202.00015L2.00015%2023.4144L0.585938%2022.0002L22.0002%200.585938L23.4144%202.00015Z%22%3E%3C%2Fpath%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M2.00015%200.585938L23.4144%2022.0002L22.0002%2023.4144L0.585938%202.00015L2.00015%200.585938Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E');
}

* {
    -webkit-tap-highlight-color: #fff0;
}

body {
    font-size: 12px;
    -ms-text-size-adjust: none;
    -moz-text-size-adjust: none;
    -o-text-size-adjust: none;
    -webkit-text-size-adjust: none;
    background-color: #fff;
}

.overflow-hidden {
    overflow: hidden;
}

.ladi-transition {
    transition: all 150ms linear 0s;
}

.z-index-1 {
    z-index: 1;
}

.opacity-0 {
    opacity: 0;
}

.height-0 {
    height: 0 !important;
}

.pointer-events-none {
    pointer-events: none;
}

.transition-parent-collapse-height {
    transition: height 150ms linear 0s;
}

.transition-parent-collapse-top {
    transition: top 150ms linear 0s;
}

.transition-readmore {
    transition: height 350ms linear 0s;
}

.transition-collapse {
    transition: height 150ms linear 0s;
}

body.grab {
    cursor: grab;
}

.ladi-wraper {
    width: 100%;
    min-height: 100%;
    overflow: hidden;
    touch-action: manipulation;
}

.ladi-container {
    position: relative;
    margin: 0 auto;
    height: 100%;
}

.ladi-overlay {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    pointer-events: none;
}

.ladi-element {
    position: absolute;
}

@media (hover: hover) {
    .ladi-check-hover {
        opacity: 0;
    }
}

.ladi-section {
    margin: 0 auto;
    position: relative;
}

.ladi-section[data-tab-id] {
    display: none;
}

.ladi-section.selected[data-tab-id] {
    display: block;
}

.ladi-section .ladi-section-background {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    overflow: hidden;
}

.ladi-box {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.ladi-frame {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.ladi-frame-bg .ladi-frame-background {
    height: 100%;
    width: 100%;
    pointer-events: none;
    transition: inherit;
}

.ladi-frame-bg:not(.ladi-frame) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

#SECTION_POPUP .ladi-container {
    z-index: 90000070;
}

#SECTION_POPUP .ladi-container > .ladi-element {
    z-index: 90000070;
    position: fixed;
    display: none;
}

#SECTION_POPUP .ladi-container > .ladi-element[data-fixed-close='true'] {
    position: relative !important;
}

#SECTION_POPUP .ladi-container > .ladi-element.hide-visibility {
    display: block !important;
    visibility: hidden !important;
}

#SECTION_POPUP .popup-close {
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 9000000080;
    cursor: pointer;
    width: 16px;
    height: 16px;
    margin: 10px;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url('data:image/svg+xml;utf8, %3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%23000%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M23.4144%202.00015L2.00015%2023.4144L0.585938%2022.0002L22.0002%200.585938L23.4144%202.00015Z%22%3E%3C%2Fpath%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M2.00015%200.585938L23.4144%2022.0002L22.0002%2023.4144L0.585938%202.00015L2.00015%200.585938Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E');
}

.ladi-popup {
    position: absolute;
    width: 100%;
    height: 100%;
}

.ladi-popup .ladi-popup-background {
    height: 100%;
    width: 100%;
    pointer-events: none;
}

.ladi-button {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.ladi-button:active {
    transform: translateY(2px);
    transition: transform 0.2s linear;
}

.ladi-button .ladi-button-background {
    height: 100%;
    width: 100%;
    pointer-events: none;
    transition: inherit;
}

.ladi-button > .ladi-button-headline,
.ladi-button > .ladi-button-shape {
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    left: 0 !important;
    display: table;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.ladi-button > .ladi-button-shape .ladi-shape {
    margin: auto;
    top: 0;
    bottom: 0;
}

.ladi-button > .ladi-button-headline .ladi-headline {
    display: table-cell;
    vertical-align: middle;
}

.ladi-checkout-product-cart-icon .ladi-cart-number {
    position: absolute;
    top: -2px;
    right: -7px;
    background: #f36e36;
    text-align: center;
    min-width: 18px;
    min-height: 18px;
    font-size: 12px;
    font-weight: 700;
    color: #fff;
    border-radius: 100%;
    z-index: 90000000;
    padding: 3px 4px;
}

.ladi-checkout-product-add-to-cart .ladi-button .loading-dots {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ladi-checkout-product-add-to-cart .ladi-button .loading-dots p {
    display: inline-block;
    font-size: 32px;
    line-height: 1;
    animation: 0.6s infinite loading;
}

.ladi-checkout-product-add-to-cart .ladi-button .loading-dots p:first-child {
    animation-delay: 0s;
}

.ladi-checkout-product-add-to-cart .ladi-button .loading-dots p:nth-child(2) {
    animation-delay: 0.2s;
}

.ladi-checkout-product-add-to-cart .ladi-button .loading-dots p:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes loading {
    0%,
    100% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }
}

.ladi-form .ladi-form-checkout-bump-offer-check.multiple.checked:before,
.ladi-form .ladi-form-checkout-payment-check.multiple.checked:before {
    --url: url('data:image/svg+xml,%0A%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M18.71%207.20998C18.617%207.11625%2018.5064%207.04186%2018.3846%206.99109C18.2627%206.94032%2018.132%206.91418%2018%206.91418C17.868%206.91418%2017.7373%206.94032%2017.6154%206.99109C17.4936%207.04186%2017.383%207.11625%2017.29%207.20998L9.84%2014.67L6.71%2011.53C6.61348%2011.4367%206.49954%2011.3634%206.37468%2011.3142C6.24983%2011.265%206.1165%2011.2409%205.98232%2011.2432C5.84814%2011.2455%205.71573%2011.2743%205.59265%2011.3278C5.46957%2011.3812%205.35824%2011.4585%205.265%2011.555C5.17176%2011.6515%205.09845%2011.7654%205.04924%2011.8903C5.00004%2012.0152%204.97591%2012.1485%204.97823%2012.2827C4.98055%2012.4168%205.00928%2012.5492%205.06277%2012.6723C5.11627%2012.7954%205.19348%2012.9067%205.29%2013L9.13%2016.84C9.22296%2016.9337%209.33356%2017.0081%209.45542%2017.0589C9.57728%2017.1096%209.70799%2017.1358%209.84%2017.1358C9.97201%2017.1358%2010.1027%2017.1096%2010.2246%2017.0589C10.3464%2017.0081%2010.457%2016.9337%2010.55%2016.84L18.71%208.67998C18.8115%208.58634%2018.8925%208.47269%2018.9479%208.34619C19.0033%208.21969%2019.0319%208.08308%2019.0319%207.94498C19.0319%207.80688%2019.0033%207.67028%2018.9479%207.54378C18.8925%207.41728%2018.8115%207.30363%2018.71%207.20998Z%22%20fill%3D%22%231852FA%22%2F%3E%0A%3C%2Fsvg%3E%0A');
    pointer-events: none;
    top: -1px;
    left: -1px;
    transform: none;
}

.ladi-form,
.ladi-form .ladi-form-item-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.ladi-form > .ladi-element,
.ladi-form > .ladi-element .ladi-form-item-container,
.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-checkbox-item
    span[data-checked='true'],
.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control:not(.ladi-form-control-select) {
    text-transform: inherit;
    text-decoration: inherit;
    text-align: inherit;
    letter-spacing: inherit;
    color: inherit;
    background-size: inherit;
    background-attachment: inherit;
    background-origin: inherit;
}

.ladi-form .ladi-button > .ladi-button-headline {
    color: initial;
    font-size: initial;
    font-weight: initial;
    text-transform: initial;
    text-decoration: initial;
    font-style: initial;
    text-align: initial;
    letter-spacing: initial;
    line-height: initial;
}

.ladi-form [data-form-checkout-item='bump_offer'] .ladi-form-item,
.ladi-form > [data-quantity='true'] .ladi-form-item-container {
    overflow: hidden;
}

.ladi-form > .ladi-element .ladi-form-item-container .ladi-form-item {
    text-transform: inherit;
    text-decoration: inherit;
    text-align: inherit;
    letter-spacing: inherit;
    color: inherit;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item-background {
    background-size: inherit;
    background-attachment: inherit;
    background-origin: inherit;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control-select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-size: 9px 6px !important;
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    padding-right: 24px;
    text-transform: inherit;
    text-align: inherit;
    letter-spacing: inherit;
    color: inherit;
    background-size: inherit;
    background-attachment: inherit;
    background-origin: inherit;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-checkbox-item,
.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-checkbox-item
    span[data-checked='false'] {
    text-transform: inherit;
    text-align: inherit;
    letter-spacing: inherit;
    background-size: inherit;
    background-attachment: inherit;
    background-origin: inherit;
    color: inherit;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control-select-2 {
    width: calc(100% / 2 - 5px);
    max-width: calc(100% / 2 - 5px);
    min-width: calc(100% / 2 - 5px);
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control-select-2:nth-child(3),
.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control-select-3:nth-child(3),
.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control-select-3:nth-child(4) {
    margin-left: 7.5px;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control-select-3 {
    width: calc(100% / 3 - 5px);
    max-width: calc(100% / 3 - 5px);
    min-width: calc(100% / 3 - 5px);
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control-select
    option {
    color: initial;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-control-select:not([data-selected='']) {
    text-decoration: inherit;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-checkbox-item {
    text-decoration: inherit;
    vertical-align: middle;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-checkbox-box-item {
    display: inline-block;
    width: fit-content;
}

.ladi-form
    > .ladi-element
    .ladi-form-item-container
    .ladi-form-item
    .ladi-form-checkbox-item
    span {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.ladi-form .ladi-form-item-title-value {
    font-weight: 700;
    word-break: break-word;
}

.ladi-form .ladi-form-label-container {
    position: relative;
    width: 100%;
}

.ladi-form .ladi-form-control-file {
    background-repeat: no-repeat;
    background-position: calc(100% - 5px) center;
}

.ladi-form .ladi-form-label-container .ladi-form-label-item {
    display: inline-block;
    cursor: pointer;
    position: relative;
    border-radius: 0 !important;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.ladi-form .ladi-form-label-container .ladi-form-label-item.image {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.ladi-form .ladi-form-label-container .ladi-form-label-item.no-value {
    display: none !important;
}

.ladi-form .ladi-form-label-container .ladi-form-label-item.text.disabled {
    opacity: 0.35;
}

.ladi-form .ladi-form-label-container .ladi-form-label-item.image.disabled {
    opacity: 0.2;
}

.ladi-form .ladi-form-label-container .ladi-form-label-item.color.disabled {
    opacity: 0.15;
}

.ladi-form .ladi-form-label-container .ladi-form-label-item.selected:before {
    content: '';
    width: 0;
    height: 0;
    bottom: -1px;
    right: -1px;
    position: absolute;
    border-width: 0 0 15px 15px;
    border-color: transparent;
    border-style: solid;
}

.ladi-form .ladi-form-label-container .ladi-form-label-item.selected:after {
    content: '';
    background-image: url("data:image/svg+xml;utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' enable-background='new 0 0 12 12' viewBox='0 0 12 12' x='0' fill='%23fff' y='0'%3E%3Cg%3E%3Cpath d='m5.2 10.9c-.2 0-.5-.1-.7-.2l-4.2-3.7c-.4-.4-.5-1-.1-1.4s1-.5 1.4-.1l3.4 3 5.1-7c .3-.4 1-.5 1.4-.2s.5 1 .2 1.4l-5.7 7.9c-.2.2-.4.4-.7.4 0-.1 0-.1-.1-.1z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: bottom right;
    width: 7px;
    height: 7px;
    bottom: 0;
    right: 0;
    position: absolute;
}

.ladi-form .ladi-form-item {
    width: 100%;
    height: 100%;
    position: absolute;
}

.ladi-form .ladi-form-item-background {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
}

.ladi-form .ladi-form-item.ladi-form-checkbox {
    height: auto;
    padding: 0 5px;
}

.ladi-form .ladi-form-item .ladi-form-control {
    background-color: transparent;
    min-width: 100%;
    min-height: 100%;
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: 100%;
    padding: 0 5px;
    color: inherit;
    font-size: inherit;
    border: none;
}

.ladi-form
    .ladi-form-item.ladi-form-checkbox.ladi-form-checkbox-vertical
    .ladi-form-checkbox-item {
    margin-top: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    display: flex;
    align-items: center;
    border: none;
}

.ladi-form
    .ladi-form-item.ladi-form-checkbox.ladi-form-checkbox-horizontal
    .ladi-form-checkbox-item {
    margin-top: 0 !important;
    margin-left: 0 !important;
    margin-right: 10px !important;
    display: inline-flex;
    align-items: center;
    border: none;
    position: relative;
}

.ladi-form .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item input {
    margin-right: 5px;
    display: block;
}

.ladi-form .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item span {
    cursor: default;
    word-break: break-word;
}

.ladi-form .ladi-form-item textarea.ladi-form-control {
    resize: none;
    padding: 5px;
}

.ladi-form .ladi-button {
    cursor: pointer;
}

.ladi-form .ladi-button .ladi-headline {
    cursor: pointer;
    user-select: none;
}

.ladi-form .ladi-element .ladi-form-otp::-webkit-inner-spin-button,
.ladi-form .ladi-element .ladi-form-otp::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.ladi-form .ladi-element .ladi-form-item .button-get-code {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto 0;
    line-height: initial;
    padding: 5px 10px;
    height: max-content;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.ladi-form .ladi-element .ladi-form-item .button-get-code.hide-visibility {
    display: block !important;
    visibility: hidden !important;
}

.ladi-form .ladi-form-checkout-bump-offer-product.option-2,
.ladi-form .ladi-form-item.otp-resend .button-get-code,
.ladi-form
    [data-form-checkout-item='payment']
    .ladi-form-checkout-payment-content
    div
    > span {
    display: block;
}

.ladi-form .ladi-form-item.otp-countdown:before {
    content: attr(data-countdown-time) 's';
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto 0;
    height: max-content;
    line-height: initial;
}

.ladi-form [data-variant='true'] select option[disabled] {
    background: #fff;
    color: #b8b8b8 !important;
}

.ladi-google-recaptcha-checkbox {
    position: absolute;
    display: inline-block;
    transform: translateY(-100%);
    margin-top: -5px;
    z-index: 90000010;
}

.ladi-element[data-form-checkout-item] {
    padding: 0;
}

.ladi-form .ladi-form-checkout-title {
    margin-bottom: 8px;
}

.ladi-form .ladi-element[data-form-checkout-item] .ladi-form-item,
.ladi-form .ladi-element[data-form-checkout-item] .ladi-form-item-container {
    height: auto !important;
    position: unset !important;
}

.ladi-form .ladi-form-checkout-box {
    gap: 12px;
    display: flex;
    flex-flow: column;
    margin: 8px 12px;
    margin-top: 14px !important;
    padding-bottom: 14px;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
    border-radius: 0 !important;
}

.ladi-form .ladi-form-checkout-box:last-child {
    border: none !important;
    padding-bottom: 4px;
}

.ladi-form .ladi-form-checkout-box:first-child,
.ladi-form .ladi-form-checkout-bump-offer-product .item-product:first-child {
    margin-top: 0;
}

.ladi-form .ladi-form-checkout-payment-item {
    display: flex;
    flex-flow: row;
    gap: 12px;
    align-items: center;
    --check-size: 18px;
    --width-quantity: 65px;
}

.ladi-form .ladi-form-checkout-payment-check {
    --border-size: 1px;
    width: var(--check-size);
    height: var(--check-size);
    flex: 0 0 var(--check-size);
    border: var(--border-size) solid;
    border-radius: 100%;
    display: block;
    position: relative;
}

.ladi-form .ladi-form-checkout-payment-check.checked:before {
    content: '';
    width: calc(var(--check-size) / 2);
    height: calc(var(--check-size) / 2);
    border-radius: inherit;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(
        calc(50% - var(--border-size)),
        calc(50% - var(--border-size))
    );
}

.ladi-form .ladi-form-checkout-bump-offer-check.multiple,
.ladi-form .ladi-form-checkout-payment-check.multiple {
    border-radius: 4px;
}

.ladi-form .ladi-form-checkout-payment-check.multiple.checked:before {
    content: '';
    -webkit-mask-image: var(--url);
    mask-image: var(--url);
    width: var(--check-size);
    height: var(--check-size);
    -webkit-mask-size: var(--check-size);
    mask-size: var(--check-size);
    background-color: #fff;
    position: absolute;
}

.ladi-form .ladi-form-checkout-payment-content,
.ladi-form .ladi-form-checkout-product-content {
    display: flex;
    flex-flow: row;
    --gap: 10px;
    gap: var(--gap);
    align-items: center;
    width: calc(
        100% - var(--width-quantity) - var(--check-size) - var(--gap) * 2
    );
}

.ladi-form .ladi-form-checkout-payment-content img,
.ladi-form .ladi-form-checkout-product-content img {
    width: 36px;
    max-height: 36px;
    border-radius: 4px;
}

.ladi-form .ladi-form-checkout-payment-content div,
.ladi-form .ladi-form-checkout-product-content div {
    display: flex;
    flex-flow: column;
}

.ladi-form .ladi-form-checkout-payment-content div > span,
.ladi-form .ladi-form-checkout-product-content div > span {
    font-size: inherit;
}

.ladi-form .ladi-form-checkout-payment-content div > span.small,
.ladi-form .ladi-form-checkout-product-content div > span.small {
    font-size: 80%;
    opacity: 0.8;
}

.ladi-form .ladi-form-checkout-payment-content div .price.price-compare,
.ladi-form .ladi-form-checkout-product-content div .price.price-compare {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-direction: row !important;
}

.ladi-form .ladi-form-checkout-payment-quantity,
.ladi-form .ladi-form-checkout-product-quantity {
    position: relative;
    --icon-size: calc(var(--check-size) * 0.8);
}

.ladi-form .ladi-form-checkout-payment-quantity input,
.ladi-form .ladi-form-checkout-product-quantity input {
    padding: 4px calc(var(--check-size)) 4px 8px;
    border-radius: 8px;
    position: relative;
    width: var(--width-quantity);
    min-height: 34px;
    border: 1px solid;
    background-color: transparent;
    top: 0;
    left: 0;
    display: block;
}

.ladi-form
    .ladi-form-checkout-payment-quantity
    input::-webkit-inner-spin-button,
.ladi-form
    .ladi-form-checkout-payment-quantity
    input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.ladi-form .ladi-form-checkout-payment-quantity .up {
    width: var(--icon-size);
    height: var(--icon-size);
    top: -1px;
    right: 5px;
    cursor: pointer;
    display: block;
    position: absolute;
}

.ladi-form .ladi-form-checkout-payment-quantity .up:before {
    content: '';
    --url: url('data:image/svg+xml,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M3.14645%206.14645C3.34171%205.95118%203.65829%205.95118%203.85355%206.14645L8%2010.2929L12.1464%206.14645C12.3417%205.95118%2012.6583%205.95118%2012.8536%206.14645C13.0488%206.34171%2013.0488%206.65829%2012.8536%206.85355L8.35355%2011.3536C8.15829%2011.5488%207.84171%2011.5488%207.64645%2011.3536L3.14645%206.85355C2.95118%206.65829%202.95118%206.34171%203.14645%206.14645Z%22%20fill%3D%22black%22%20transform%3D%22rotate(180%208%208)%22%2F%3E%3C%2Fsvg%3E');
    -webkit-mask-image: var(--url);
    mask-image: var(--url);
    display: block;
    position: absolute;
    width: var(--icon-size);
    height: var(--icon-size);
    pointer-events: none;
    top: 4px;
    left: 2px;
    -webkit-mask-size: var(--icon-size);
    mask-size: var(--icon-size);
}

.ladi-form .ladi-form-checkout-payment-quantity .down {
    width: var(--icon-size);
    height: var(--icon-size);
    right: 5px;
    cursor: pointer;
    display: block;
    position: absolute;
    bottom: 3px;
}

.ladi-form .ladi-form-checkout-payment-quantity .down:before {
    content: '';
    --url: url('data:image/svg+xml,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M3.14645%206.14645C3.34171%205.95118%203.65829%205.95118%203.85355%206.14645L8%2010.2929L12.1464%206.14645C12.3417%205.95118%2012.6583%205.95118%2012.8536%206.14645C13.0488%206.34171%2013.0488%206.65829%2012.8536%206.85355L8.35355%2011.3536C8.15829%2011.5488%207.84171%2011.5488%207.64645%2011.3536L3.14645%206.85355C2.95118%206.65829%202.95118%206.34171%203.14645%206.14645Z%22%20fill%3D%22black%22%2F%3E%0A%3C%2Fsvg%3E');
    -webkit-mask-image: var(--url);
    mask-image: var(--url);
    display: block;
    position: absolute;
    width: var(--icon-size);
    height: var(--icon-size);
    pointer-events: none;
    left: 2px;
    -webkit-mask-size: var(--icon-size);
    mask-size: var(--icon-size);
}

.ladi-form [data-form-checkout-item='payment'] .ladi-form-item {
    display: table;
}

.ladi-form
    [data-form-checkout-item='payment']
    .ladi-form-item
    .ladi-form-checkout-payment-content {
    width: calc(100% - 18px);
}

.ladi-form
    [data-form-checkout-item='payment']
    .ladi-form-item
    .ladi-form-checkout-payment-content
    div {
    display: table-cell;
    vertical-align: middle;
    padding: 0 6px;
    cursor: pointer;
    width: 100%;
    position: relative;
}

.ladi-form
    [data-form-checkout-item='payment']
    .ladi-form-item
    .ladi-form-checkout-payment-content
    div.arrow:before {
    content: '';
    --url: url('data:image/svg+xml,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M6.14645%203.14645C6.34171%202.95118%206.65829%202.95118%206.85355%203.14645L11.3536%207.64645C11.5488%207.84171%2011.5488%208.15829%2011.3536%208.35355L6.85355%2012.8536C6.65829%2013.0488%206.34171%2013.0488%206.14645%2012.8536C5.95118%2012.6583%205.95118%2012.3417%206.14645%2012.1464L10.2929%208L6.14645%203.85355C5.95118%203.65829%205.95118%203.34171%206.14645%203.14645Z%22%20fill%3D%22black%22%2F%3E%0A%3C%2Fsvg%3E');
    -webkit-mask-image: var(--url);
    mask-image: var(--url);
    position: absolute;
    width: 20px;
    height: 20px;
    top: 0;
    right: 0;
    bottom: 0;
    display: block;
    margin: auto;
    -webkit-mask-size: 100%;
    mask-size: 100%;
}

.ladi-form [data-form-checkout-item='total_price'] {
    display: flex;
    flex-flow: column;
    gap: 3px;
    justify-content: center;
}

.ladi-form [data-form-checkout-item='total_price'] .line {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
}

.ladi-form [data-form-checkout-item='total_price'] .title-number-price.big,
.ladi-form [data-form-checkout-item='total_price'] .title-price.big {
    font-weight: 700;
    font-size: 130%;
}

.ladi-form [data-form-checkout-item='total_price'] .title-number-price {
    font-size: 115%;
}

.ladi-form [data-form-checkout-item='total_price'] .space {
    border-top: 1px solid;
    margin: 15px 0 10px;
}

.ladi-form .ladi-form-checkout-bump-offer-checkbox {
    display: flex;
    flex-flow: row;
    gap: 8px;
    padding: 8px;
    margin: 12px 8px 0;
    border-radius: 8px;
    align-items: center;
    --check-size-bumpoffer: 18px;
    --width-quantity-bumpoffer: 60px;
}

.ladi-form .ladi-form-checkout-bump-offer-checkbox.checkbox-bump-offer-1 {
    background-color: #3c72f9;
    margin: 0;
}

.ladi-form .ladi-form-checkout-bump-offer-checkbox.checkbox-bump-offer-1 p {
    color: #fff;
}

.ladi-form .ladi-form-checkout-bump-offer-checkbox.checkbox-bump-offer-2 {
    background-color: #fde298;
    margin: 12px 0 4px;
}

.ladi-form .ladi-form-checkout-bump-offer-checkbox.checkbox-bump-offer-3 {
    background-color: #f3f4f5;
    margin: 8px 0 0;
    padding: 4px 8px;
    width: max-content;
}

.ladi-form
    .ladi-form-checkout-bump-offer-checkbox.checkbox-bump-offer-3
    .ladi-form-checkout-bump-offer-check {
    width: 12px;
    height: 12px;
    flex: 0 0 12px;
}

.ladi-form .ladi-form-checkout-bump-offer-check {
    --border-size: 1px;
    width: var(--check-size-bumpoffer);
    height: var(--check-size-bumpoffer);
    flex: 0 0 var(--check-size-bumpoffer);
    border: var(--border-size) solid #cfd3d8;
    background-color: #fff;
    border-radius: 100%;
    display: block;
    position: relative;
}

.ladi-form .ladi-form-checkout-bump-offer-check.checked:before {
    content: '';
    width: calc(var(--check-size-bumpoffer) / 2);
    height: calc(var(--check-size-bumpoffer) / 2);
    border-radius: inherit;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(
        calc(50% - var(--border-size)),
        calc(50% - var(--border-size))
    );
}

.ladi-form .ladi-form-checkout-bump-offer-check.multiple.checked:before {
    content: '';
    -webkit-mask-image: var(--url);
    mask-image: var(--url);
    width: var(--check-size-bumpoffer);
    height: var(--check-size-bumpoffer);
    -webkit-mask-size: var(--check-size-bumpoffer);
    mask-size: var(--check-size-bumpoffer);
    background-color: #000;
    position: absolute;
}

.ladi-form
    .ladi-form-checkout-bump-offer-checkbox.checkbox-bump-offer-3
    .ladi-form-checkout-bump-offer-check.multiple.checked:before {
    top: -5px;
    left: -3px;
}

.ladi-form .ladi-form-checkout-bump-offer-product {
    display: flex;
    flex-flow: row;
    gap: 16px;
    margin: 8px 12px;
    align-items: center;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
    border-radius: 0 !important;
}

.ladi-form .ladi-form-checkout-bump-offer-product.option-1 {
    display: block !important;
    padding-bottom: 8px;
}

.ladi-form .ladi-form-item:last-child .ladi-form-checkout-bump-offer-product {
    border: none !important;
}

.ladi-form .ladi-form-checkout-bump-offer-product .item-product {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    margin-top: 16px;
    margin-bottom: 12px;
}

.ladi-form .ladi-form-checkout-bump-offer-product .item-product:last-child {
    margin-bottom: 0;
}

.ladi-form .ladi-form-checkout-bump-offer-product .item-product img {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    overflow: hidden;
}

.ladi-form
    .ladi-form-checkout-bump-offer-product
    .item-product
    .item-product-title {
    font-size: 90%;
    opacity: 0.6;
}

.ladi-form
    .ladi-form-checkout-bump-offer-product
    .item-product
    .item-product-description {
    color: #9fa7b1;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.ladi-form .ladi-form-checkout-bump-offer-product .item-detail {
    display: flex;
    flex-direction: column;
}

.ladi-form
    .ladi-form-checkout-bump-offer-product
    .item-detail
    .shock-promotion-label {
    background-color: #ef9300;
    width: max-content;
    border-radius: 4px;
    padding: 2px 8px;
    color: #fff;
    margin-bottom: 4px;
    font-weight: 600;
}

.ladi-form .ladi-form-checkout-bump-offer-product .item-detail .pr-name {
    font-weight: 400;
    font-size: 120%;
    line-height: 1.4;
}

.ladi-form .ladi-form-checkout-bump-offer-product .item-detail .pr-price {
    font-weight: 400;
    line-height: 1.4;
}

.ladi-form .ladi-form-checkout-bump-offer-product .item-detail span a {
    text-decoration: line-through;
    opacity: 0.6;
}

.ladi-form .ladi-form-checkout-bump-offer-product .item-detail .special {
    background-color: #e01a1a;
    padding: 2px 8px;
    margin-right: 6px;
    color: #fff;
    border-radius: 4px;
    font-size: 80%;
}

.ladi-form [data-form-checkout-item='coupon_code'] .content-discount-block {
    padding: 12px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.ladi-form [data-form-checkout-item='coupon_code'] .ladi-form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.ladi-form
    [data-form-checkout-item='coupon_code']
    .content-discount-block
    .title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ladi-form
    [data-form-checkout-item='coupon_code']
    .content-discount-block
    .placeholder
    span,
.ladi-form
    [data-form-checkout-item='coupon_code']
    .content-discount-block
    .title
    span {
    font-weight: 400;
    font-size: 100%;
    line-height: 1.4;
}

.ladi-form
    [data-form-checkout-item='coupon_code']
    .content-discount-block
    .title
    i {
    width: 16px;
    height: 16px;
    mask-image: url(https://w.ladicdn.com/ladiui/icons/ldicon-discount-coupon.svg);
    display: inline-block;
    mask-size: cover;
}

.ladi-form
    [data-form-checkout-item='coupon_code']
    .content-discount-block
    .placeholder {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.ladi-form
    [data-form-checkout-item='coupon_code']
    .content-discount-block
    .placeholder
    i {
    width: 16px;
    height: 16px;
    mask-image: url(https://w.ladicdn.com/ladiui/icons/new-ldicon-arrow-left.svg);
    display: inline-block;
    mask-size: cover;
    background-color: #6d6d6d !important;
}

.ladi-group {
    position: absolute;
    width: 100%;
    height: 100%;
}

.ladi-accordion {
    position: absolute;
    width: 100%;
    height: 100%;
}

.ladi-accordion .ladi-accordion-shape {
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    left: 0 !important;
    display: table;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.ladi-accordion
    .accordion-menu
    > .ladi-frame
    > .ladi-element:not(.ladi-accordion-shape) {
    z-index: 2;
}

.ladi-shape {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.ladi-shape .ladi-cart-number {
    position: absolute;
    top: -2px;
    right: -7px;
    background: #f36e36;
    text-align: center;
    width: 18px;
    height: 18px;
    line-height: 18px;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    border-radius: 100%;
}

.ladi-image {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.ladi-image .ladi-image-background {
    background-repeat: no-repeat;
    background-position: left top;
    background-size: cover;
    background-attachment: scroll;
    background-origin: content-box;
    position: absolute;
    margin: 0 auto;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.ladi-headline {
    width: 100%;
    display: inline-block;
    word-break: break-word;
    background-size: cover;
    background-position: center center;
}

.ladi-headline a {
    text-decoration: underline;
}

.ladi-paragraph {
    width: 100%;
    display: inline-block;
    word-break: break-word;
}

.ladi-paragraph a {
    text-decoration: underline;
}

.ladi-list-paragraph {
    width: 100%;
    display: inline-block;
}

.ladi-list-paragraph a {
    text-decoration: underline;
}

.ladi-list-paragraph ul li {
    position: relative;
    counter-increment: linum;
}

.ladi-list-paragraph ul li:before {
    position: absolute;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    left: 0;
}

.ladi-list-paragraph ul li:last-child {
    padding-bottom: 0 !important;
}

.ladi-line {
    position: relative;
}

.ladi-line .ladi-line-container {
    border-bottom: 0 !important;
    border-right: 0 !important;
    width: 100%;
    height: 100%;
}

a[data-action] {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    cursor: pointer;
}

a:visited {
    color: inherit;
}

a:link {
    color: inherit;
}

[data-opacity='0'] {
    opacity: 0;
}

[data-hidden='true'] {
    display: none;
}

[data-action='true'] {
    cursor: pointer;
}

.ladi-hidden {
    display: none;
}

.ladi-animation-hidden {
    visibility: hidden !important;
    opacity: 0 !important;
}

.element-click-selected {
    cursor: pointer;
}

.is-2nd-click {
    cursor: pointer;
}

.ladi-button-shape.is-2nd-click,
.ladi-accordion-shape.is-2nd-click {
    z-index: 3;
}

.backdrop-popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 90000060;
}

.backdrop-dropbox {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 90000040;
}

.ladi-lazyload {
    background-image: none !important;
}

.ladi-list-paragraph ul li.ladi-lazyload:before {
    background-image: none !important;
}

@media (min-width: 768px) {
}

@media (max-width: 767px) {
    .ladi-element.ladi-auto-scroll {
        overflow-x: auto;
        overflow-y: hidden;
        width: 100% !important;
        left: 0 !important;
        -webkit-overflow-scrolling: touch;
    }

    [data-hint]:not([data-timeout-id-copied]):before,
    [data-hint]:not([data-timeout-id-copied]):after {
        display: none !important;
    }

    .ladi-section.ladi-auto-scroll {
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
    }
}
@media (min-width: 768px) {
    #IMAGE225,
    #BUTTON33,
    #HEADLINE374,
    #GROUP268,
    #HEADLINE377,
    #LINE29,
    #HEADLINE378,
    #LINE30,
    #HEADLINE379,
    #LINE32,
    #IMAGE262,
    #IMAGE263,
    #SHAPE34,
    #HEADLINE389 {
        pointer-events: none !important;
    }
}

@media (max-width: 767px) {
    #IMAGE225,
    #BUTTON33,
    #HEADLINE374,
    #GROUP268,
    #HEADLINE377,
    #LINE29,
    #HEADLINE378,
    #LINE30,
    #HEADLINE379,
    #LINE32,
    #IMAGE262,
    #IMAGE263,
    #SHAPE34,
    #HEADLINE389 {
        pointer-events: none !important;
    }
}
body {
    direction: ltr;
}

@media (min-width: 768px) {
    .ladi-section .ladi-container {
        width: 1200px;
    }
}

@media (max-width: 767px) {
    .ladi-section .ladi-container {
        width: 420px;
    }
}

@font-face {
    font-family: 'QmFpSmFtanVyZWUtUmVndWxhcidGY';
    src: url('https://w.ladicdn.com/57e4d138957904ae180e0544/baijamjuree-regular-20240101174449-hcav6.ttf')
        format('truetype');
}

@font-face {
    font-family: 'VGlrVGrRGlzcGxheSSZWdbGFyLmZg';
    src: url('https://w.ladicdn.com/57e4d138957904ae180e0544/tiktokdisplay-regular-20241029063328-ek5ow.otf');
}

@font-face {
    font-family: 'VGlrVGrRGlzcGxheSCbxkLmZg';
    src: url('https://w.ladicdn.com/57e4d138957904ae180e0544/tiktokdisplay-bold-20241029063328-lcnbr.otf');
}

body {
    font-family: QmFpSmFtanVyZWUtUmVndWxhcidGY;
}
#SECTION43 > .ladi-section-background {
    background-size: cover;
    background-origin: content-box;
    background-position: 50% 0%;
    background-repeat: repeat;
    background-attachment: scroll;
}

#IMAGE227 > .ladi-image > .ladi-image-background,
#IMAGE262 > .ladi-image > .ladi-image-background,
#IMAGE263 > .ladi-image > .ladi-image-background,
#BOX130,
#BOX129,
#HEADLINE374,
#GROUP272,
#BOX131,
#IMAGE228 > .ladi-image > .ladi-image-background,
#BOX134,
#IMAGE229 > .ladi-image > .ladi-image-background,
#BOX135,
#IMAGE230 > .ladi-image > .ladi-image-background,
#BOX136,
#IMAGE231 > .ladi-image > .ladi-image-background,
#BOX137,
#IMAGE232 > .ladi-image > .ladi-image-background,
#BOX138,
#IMAGE233 > .ladi-image > .ladi-image-background,
#BOX167,
#IMAGE240 > .ladi-image > .ladi-image-background,
#IMAGE242 > .ladi-image > .ladi-image-background,
#IMAGE243 > .ladi-image > .ladi-image-background,
#BOX148,
#IMAGE244 > .ladi-image > .ladi-image-background,
#BOX156,
#IMAGE252 > .ladi-image > .ladi-image-background,
#BOX157,
#IMAGE253 > .ladi-image > .ladi-image-background,
#IMAGE257 > .ladi-image > .ladi-image-background,
#FORM_ITEM43,
#ACCORDION_MENU25,
#ACCORDION_MENU27,
#ACCORDION_MENU19,
#ACCORDION_MENU21,
#ACCORDION_MENU29,
#POPUP2,
#IMAGE225 > .ladi-image > .ladi-image-background {
    top: 0px;
    left: 0px;
}

#IMAGE262,
#GROUP273,
#GROUP274,
#IMAGE225 {
    top: 0px;
}

#IMAGE262.ladi-animation > .ladi-image {
    animation-name: fadeInUp;
    animation-delay: 0s;
    animation-duration: 5s;
    animation-iteration-count: infinite;
}

#IMAGE263,
#BUTTON_TEXT34,
#GROUP319,
#HEADLINE389,
#GROUP316,
#HEADLINE378,
#BUTTON_TEXT38,
#ACCORDION_CONTENT24,
#ACCORDION_CONTENT26,
#ACCORDION_CONTENT18,
#ACCORDION_CONTENT20,
#ACCORDION_CONTENT28,
#BUTTON_TEXT33 {
    left: 0px;
}

#IMAGE263.ladi-animation > .ladi-image {
    animation-name: fadeInDown;
    animation-delay: 0s;
    animation-duration: 5s;
    animation-iteration-count: infinite;
}

#BOX130 > .ladi-box,
#BOX167 > .ladi-box {
    border-width: 1px;
    border-radius: 1009px;
    border-style: solid;
    border-color: rgb(222, 118, 0);
    background-image: linear-gradient(rgb(255, 175, 24), rgb(253, 149, 1));
    background-color: initial;
    background-size: initial;
    background-origin: initial;
    background-position: initial;
    background-repeat: initial;
    background-attachment: initial;
}

#BOX130 > .ladi-box,
#BOX167 > .ladi-box,
#BOX158 > .ladi-box,
#BUTTON33 > .ladi-button > .ladi-button-background {
    -webkit-background-clip: initial;
}

#BOX130 > .ladi-box:hover,
#BOX129 > .ladi-box:hover,
#BOX131 > .ladi-box:hover,
#BOX134 > .ladi-box:hover,
#BOX135 > .ladi-box:hover,
#BOX136 > .ladi-box:hover,
#BOX137 > .ladi-box:hover,
#BOX138 > .ladi-box:hover,
#BOX167 > .ladi-box:hover,
#BOX139 > .ladi-box:hover,
#BOX146 > .ladi-box:hover,
#BOX147 > .ladi-box:hover,
#BOX148 > .ladi-box:hover,
#BOX156 > .ladi-box:hover,
#BOX157 > .ladi-box:hover,
#BOX158 > .ladi-box:hover,
#ACCORDION_CONTENT24 > .ladi-frame:hover,
#ACCORDION_CONTENT24 > .ladi-frame:hover ~ .ladi-frame-bg,
#PARAGRAPH96 > .ladi-paragraph:hover,
#ACCORDION_MENU25 > .ladi-frame:hover,
#ACCORDION_MENU25 > .ladi-frame:hover ~ .ladi-frame-bg,
#ACCORDION_SHAPE31:hover > .ladi-shape,
#HEADLINE386 > .ladi-headline:hover,
#ACCORDION_CONTENT26 > .ladi-frame:hover,
#ACCORDION_CONTENT26 > .ladi-frame:hover ~ .ladi-frame-bg,
#PARAGRAPH97 > .ladi-paragraph:hover,
#ACCORDION_MENU27 > .ladi-frame:hover,
#ACCORDION_MENU27 > .ladi-frame:hover ~ .ladi-frame-bg,
#ACCORDION_SHAPE32:hover > .ladi-shape,
#HEADLINE387 > .ladi-headline:hover,
#ACCORDION_CONTENT18 > .ladi-frame:hover,
#ACCORDION_CONTENT18 > .ladi-frame:hover ~ .ladi-frame-bg,
#PARAGRAPH93 > .ladi-paragraph:hover,
#ACCORDION_MENU19 > .ladi-frame:hover,
#ACCORDION_MENU19 > .ladi-frame:hover ~ .ladi-frame-bg,
#ACCORDION_SHAPE28:hover > .ladi-shape,
#HEADLINE383 > .ladi-headline:hover,
#ACCORDION_CONTENT20 > .ladi-frame:hover,
#ACCORDION_CONTENT20 > .ladi-frame:hover ~ .ladi-frame-bg,
#PARAGRAPH94 > .ladi-paragraph:hover,
#ACCORDION_MENU21 > .ladi-frame:hover,
#ACCORDION_MENU21 > .ladi-frame:hover ~ .ladi-frame-bg,
#ACCORDION_SHAPE29:hover > .ladi-shape,
#HEADLINE384 > .ladi-headline:hover,
#ACCORDION_CONTENT28 > .ladi-frame:hover,
#ACCORDION_CONTENT28 > .ladi-frame:hover ~ .ladi-frame-bg,
#PARAGRAPH98 > .ladi-paragraph:hover,
#ACCORDION_MENU29 > .ladi-frame:hover,
#ACCORDION_MENU29 > .ladi-frame:hover ~ .ladi-frame-bg,
#ACCORDION_SHAPE33:hover > .ladi-shape,
#HEADLINE388 > .ladi-headline:hover,
#BOX128 > .ladi-box:hover,
#BUTTON33 > .ladi-button:hover,
#BUTTON_TEXT33 > .ladi-headline:hover {
    opacity: 1;
}

#BUTTON34 > .ladi-button > .ladi-button-background,
#BOX131 > .ladi-box,
#BOX134 > .ladi-box,
#BOX135 > .ladi-box,
#BOX136 > .ladi-box,
#BOX137 > .ladi-box,
#BOX138 > .ladi-box,
#BUTTON38 > .ladi-button > .ladi-button-background,
#BOX139 > .ladi-box,
#BOX146 > .ladi-box,
#BOX147 > .ladi-box,
#BOX148 > .ladi-box,
#BOX156 > .ladi-box,
#BOX157 > .ladi-box,
#FORM10 .ladi-form-item-background,
#POPUP2 > .ladi-popup > .ladi-popup-background {
    background-color: rgb(255, 255, 255);
}

#BUTTON34 > .ladi-button,
#BUTTON38 > .ladi-button {
    border-radius: 100px;
}

#BUTTON_TEXT34,
#BUTTON_TEXT38 {
    width: 224px;
}

#BUTTON_TEXT34 > .ladi-headline,
#BUTTON_TEXT38 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(222, 118, 0);
    text-align: center;
}

#GROUP268.ladi-animation > .ladi-group {
    animation-name: fadeInUp;
    animation-delay: 0s;
    animation-duration: 1s;
    animation-iteration-count: 1;
}

#BOX129 > .ladi-box {
    border-radius: 1009px;
}

#BOX129 > .ladi-box,
#BOX128 > .ladi-box {
    background-color: rgb(222, 118, 0);
}

#PARAGRAPH19 > .ladi-paragraph,
#PARAGRAPH17 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    line-height: 1.6;
    color: rgb(255, 255, 255);
    text-align: center;
}

#HEADLINE374 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    font-weight: bold;
    line-height: 1.3;
    color: rgb(0, 0, 0);
}

#HEADLINE374.ladi-animation > .ladi-headline,
#HEADLINE389.ladi-animation > .ladi-headline,
#LINE29.ladi-animation > .ladi-line,
#HEADLINE378.ladi-animation > .ladi-headline,
#LINE30.ladi-animation > .ladi-line,
#HEADLINE379.ladi-animation > .ladi-headline,
#LINE32.ladi-animation > .ladi-line {
    animation-name: fadeInLeft;
    animation-delay: 0s;
    animation-duration: 1s;
    animation-iteration-count: 1;
}

#HEADLINE389 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-weight: bold;
    line-height: 1.3;
    color: rgb(0, 0, 0);
}

#LIST_PARAGRAPH82 > .ladi-list-paragraph,
#LIST_PARAGRAPH84 > .ladi-list-paragraph,
#LIST_PARAGRAPH85 > .ladi-list-paragraph,
#LIST_PARAGRAPH86 > .ladi-list-paragraph,
#LIST_PARAGRAPH78 > .ladi-list-paragraph,
#LIST_PARAGRAPH80 > .ladi-list-paragraph,
#LIST_PARAGRAPH81 > .ladi-list-paragraph {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    color: rgb(43, 43, 43);
}

#LIST_PARAGRAPH82 ul li,
#LIST_PARAGRAPH84 ul li,
#LIST_PARAGRAPH85 ul li,
#LIST_PARAGRAPH86 ul li,
#LIST_PARAGRAPH78 ul li,
#LIST_PARAGRAPH80 ul li,
#LIST_PARAGRAPH81 ul li {
    padding-bottom: 8px;
    padding-left: 28px;
}

#LIST_PARAGRAPH82 ul li:before,
#LIST_PARAGRAPH84 ul li:before,
#LIST_PARAGRAPH85 ul li:before,
#LIST_PARAGRAPH86 ul li:before,
#LIST_PARAGRAPH78 ul li:before,
#LIST_PARAGRAPH80 ul li:before,
#LIST_PARAGRAPH81 ul li:before {
    width: 20px;
    height: 20px;
    top: 4px;
    background-image: url('data:image/svg+xml;utf8, %3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20height%3D%2269px%22%20viewBox%3D%220%20-960%20960%20960%22%20width%3D%2269px%22%20%20class%3D%22%22%20fill%3D%22rgba(222%2C%20118%2C%200%2C%201)%22%3E%3Cpath%20d%3D%22m421-298%20283-283-46-45-237%20237-120-120-45%2045%20165%20166Zm59%20218q-82%200-155-31.5t-127.5-86Q143-252%20111.5-325T80-480q0-83%2031.5-156t86-127Q252-817%20325-848.5T480-880q83%200%20156%2031.5T763-763q54%2054%2085.5%20127T880-480q0%2082-31.5%20155T763-197.5q-54%2054.5-127%2086T480-80Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E');
    content: '';
}

#SECTION46 > .ladi-section-background,
#hethong > .ladi-section-background,
#SECTION48 > .ladi-section-background,
#dangky > .ladi-section-background,
#SECTION50 > .ladi-section-background {
    background-color: rgb(248, 248, 248);
}

#HEADLINE377 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-weight: bold;
    color: rgb(0, 0, 0);
}

#HEADLINE377.ladi-animation > .ladi-headline {
    animation-name: fadeInLeft;
    animation-duration: 1s;
}

#LINE29 > .ladi-line > .ladi-line-container,
#LINE30 > .ladi-line > .ladi-line-container,
#LINE32 > .ladi-line > .ladi-line-container,
#LINE35 > .ladi-line > .ladi-line-container {
    border-top: 1px solid rgb(222, 118, 0);
    border-right: 1px solid rgb(222, 118, 0);
    border-bottom: 1px solid rgb(222, 118, 0);
    border-left: 0px !important;
}

#LINE29 > .ladi-line,
#LINE31 > .ladi-line,
#LINE30 > .ladi-line,
#LINE32 > .ladi-line,
#LINE35 > .ladi-line {
    width: 100%;
    padding: 8px 0px;
}

#LINE31 > .ladi-line > .ladi-line-container {
    border-top: 1px solid rgb(166, 166, 166);
    border-right: 1px solid rgb(166, 166, 166);
    border-bottom: 1px solid rgb(166, 166, 166);
    border-left: 0px !important;
}

#GROUP272,
#BOX131,
#GROUP273,
#BOX134,
#GROUP274,
#BOX135,
#GROUP275,
#BOX136,
#GROUP276,
#BOX137,
#GROUP277,
#BOX138 {
    width: 376.647px;
    height: 250px;
}

#BOX131 > .ladi-box,
#BOX134 > .ladi-box,
#BOX135 > .ladi-box,
#BOX136 > .ladi-box,
#BOX137 > .ladi-box,
#BOX138 > .ladi-box,
#BOX139 > .ladi-box,
#BOX146 > .ladi-box,
#BOX147 > .ladi-box,
#BOX148 > .ladi-box,
#BOX156 > .ladi-box,
#BOX157 > .ladi-box,
#BOX158 > .ladi-box {
    border-radius: 30px;
}

#PARAGRAPH22,
#PARAGRAPH23,
#PARAGRAPH24,
#PARAGRAPH25,
#PARAGRAPH26,
#PARAGRAPH27,
#PARAGRAPH28,
#PARAGRAPH29,
#PARAGRAPH30,
#PARAGRAPH31,
#PARAGRAPH33,
#PARAGRAPH49,
#PARAGRAPH50,
#PARAGRAPH61,
#PARAGRAPH68,
#PARAGRAPH69,
#PARAGRAPH70,
#PARAGRAPH71,
#PARAGRAPH72,
#PARAGRAPH73 {
    width: 314px;
}

#PARAGRAPH22,
#PARAGRAPH24,
#PARAGRAPH26,
#PARAGRAPH28,
#PARAGRAPH30,
#PARAGRAPH32,
#PARAGRAPH49,
#PARAGRAPH68,
#PARAGRAPH71 {
    top: 111px;
    left: 31.324px;
}

#PARAGRAPH22 > .ladi-paragraph,
#PARAGRAPH24 > .ladi-paragraph,
#PARAGRAPH26 > .ladi-paragraph,
#PARAGRAPH28 > .ladi-paragraph,
#PARAGRAPH30 > .ladi-paragraph,
#PARAGRAPH32 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-size: 25px;
    line-height: 1.6;
    color: rgb(0, 0, 0);
    text-align: left;
}

#PARAGRAPH23,
#PARAGRAPH25,
#PARAGRAPH27,
#PARAGRAPH29,
#PARAGRAPH31,
#PARAGRAPH33 {
    top: 156.5px;
    left: 31.324px;
}

#PARAGRAPH23 > .ladi-paragraph,
#PARAGRAPH25 > .ladi-paragraph,
#PARAGRAPH27 > .ladi-paragraph,
#PARAGRAPH29 > .ladi-paragraph,
#PARAGRAPH31 > .ladi-paragraph,
#PARAGRAPH33 > .ladi-paragraph,
#PARAGRAPH61 > .ladi-paragraph,
#PARAGRAPH70 > .ladi-paragraph,
#PARAGRAPH73 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    font-size: 18px;
    line-height: 1.6;
    color: rgb(43, 43, 43);
}

#IMAGE228,
#IMAGE228 > .ladi-image > .ladi-image-background,
#IMAGE229,
#IMAGE229 > .ladi-image > .ladi-image-background,
#IMAGE230,
#IMAGE230 > .ladi-image > .ladi-image-background,
#IMAGE231,
#IMAGE231 > .ladi-image > .ladi-image-background,
#IMAGE232,
#IMAGE232 > .ladi-image > .ladi-image-background,
#IMAGE233,
#IMAGE233 > .ladi-image > .ladi-image-background,
#IMAGE244,
#IMAGE244 > .ladi-image > .ladi-image-background,
#IMAGE252,
#IMAGE252 > .ladi-image > .ladi-image-background,
#IMAGE253,
#IMAGE253 > .ladi-image > .ladi-image-background {
    width: 67.5px;
    height: 67.5px;
}

#IMAGE228,
#IMAGE229,
#IMAGE230,
#IMAGE231,
#IMAGE232,
#IMAGE233,
#IMAGE244,
#IMAGE252,
#IMAGE253 {
    top: 36.5px;
    left: 31.324px;
}

#IMAGE228 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/dashboard-20250911041840-xam4v.png');
}

#IMAGE229 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/efficiency-20250911041841-uxk68.png');
}

#IMAGE230 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/alerts-20250911041840-bg8_p.png');
}

#IMAGE231 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/management-20250911041841-mg3qd.png');
}

#IMAGE232 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/statistics-20250911041841-vmthd.png');
}

#PARAGRAPH32 {
    width: 345px;
}

#IMAGE233 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/save-time-20250911041841-_tafo.png');
}

#SHAPE34 {
    width: 24.2384px;
    height: 25.6463px;
}

#SHAPE34.ladi-animation > .ladi-shape {
    animation-name: shake;
    animation-delay: 0s;
    animation-duration: 10s;
    animation-iteration-count: 1;
}

#SHAPE34 svg:last-child,
#ACCORDION_SHAPE31 svg:last-child,
#ACCORDION_SHAPE32 svg:last-child,
#ACCORDION_SHAPE28 svg:last-child,
#ACCORDION_SHAPE29 svg:last-child,
#ACCORDION_SHAPE33 svg:last-child {
    fill: rgb(222, 118, 0);
}

#HEADLINE378 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    font-weight: bold;
    line-height: 1.4;
    color: rgb(43, 43, 43);
}

#PARAGRAPH34 > .ladi-paragraph,
#PARAGRAPH47 > .ladi-paragraph,
#PARAGRAPH48 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    line-height: 1.6;
    color: rgb(0, 0, 0);
    text-align: left;
}

#HEADLINE379 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-weight: bold;
    line-height: 1.4;
    color: rgb(0, 0, 0);
}

#GROUP301,
#BOX148,
#GROUP302,
#BOX156,
#GROUP303,
#BOX157 {
    width: 376.647px;
}

#PARAGRAPH49 > .ladi-paragraph,
#PARAGRAPH68 > .ladi-paragraph,
#PARAGRAPH71 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-size: 25px;
    line-height: 1.6;
    color: rgb(222, 118, 0);
    text-align: left;
}

#PARAGRAPH50,
#PARAGRAPH69,
#PARAGRAPH72 {
    top: 153.5px;
    left: 31.324px;
}

#PARAGRAPH50 > .ladi-paragraph,
#PARAGRAPH69 > .ladi-paragraph,
#PARAGRAPH72 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-size: 18px;
    line-height: 1.6;
    color: rgb(0, 0, 0);
}

#IMAGE244 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/475841513_2977825802373884_6442098407338677810_n-20250915021234-skzwt.jpg');
}

#IMAGE244 > .ladi-image,
#IMAGE252 > .ladi-image,
#IMAGE253 > .ladi-image,
#BUTTON33 > .ladi-button {
    border-radius: 1000px;
}

#PARAGRAPH61 {
    left: 31.324px;
}

#IMAGE252 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/127912789_101041471866014_5816993828159069745_n-20250915022855-nvf29.jpg');
}

#PARAGRAPH70,
#PARAGRAPH73 {
    top: 199.5px;
    left: 31.324px;
}

#IMAGE253 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/nguyen-hong-quan-20250915022702-j1frr.jpg');
}

#BOX158 > .ladi-box {
    background-image: linear-gradient(
        45deg,
        rgb(222, 118, 0),
        rgb(253, 149, 1)
    );
    background-color: initial;
    background-size: initial;
    background-origin: initial;
    background-position: initial;
    background-repeat: initial;
    background-attachment: initial;
}

#PARAGRAPH76 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    line-height: 1.6;
    color: rgb(255, 255, 255);
}

#PARAGRAPH74 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    line-height: 1.4;
    color: rgb(255, 255, 255);
}

#FORM10 > .ladi-form {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    font-size: 16px;
    line-height: 1.6;
    color: rgb(43, 43, 43);
}

#FORM10 .ladi-form .ladi-form-checkout-payment-item {
    --check-size: calc(16px * 1.5);
    --width-quantity: calc(16px * 2.5 + 30px);
}

#FORM10
    .ladi-form
    .ladi-form-item.ladi-form-checkbox
    .ladi-form-checkbox-item
    span[data-checked='false'],
#FORM10
    .ladi-form
    .ladi-form-item.ladi-form-checkbox
    .ladi-form-checkbox-item
    .ladi-editing,
#FORM10
    .ladi-form
    .ladi-form-item.ladi-form-checkbox
    .ladi-form-checkbox-item
    .ladi-editing::placeholder,
#FORM10 .ladi-form .ladi-survey-option .ladi-survey-option-label,
#FORM10 .ladi-form-item .ladi-form-control::placeholder,
#FORM10 .ladi-form-item select.ladi-form-control[data-selected=''],
#FORM10 .ladi-form-checkout-payment-quantity input {
    color: rgb(43, 43, 43);
}

#FORM10:hover .overlay-checkout {
    display: flex !important;
}

#FORM10 .ladi-form-item {
    padding-left: 5px;
    padding-right: 5px;
}

#FORM10 .ladi-form-item.otp-countdown:before {
    right: 10px;
}

#FORM10 .ladi-form-item.ladi-form-checkbox {
    padding-left: 10px;
    padding-right: 10px;
}

#FORM10 .ladi-form-item-container .ladi-form-item .ladi-form-control-select {
    background-image: url('data:image/svg+xml;utf8, %3Csvg%20width%3D%2232%22%20height%3D%2224%22%20viewBox%3D%220%200%2032%2024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpolygon%20points%3D%220%2C0%2032%2C0%2016%2C24%22%20style%3D%22fill%3A%20rgb(43%2C%2043%2C%2043)%22%3E%3C%2Fpolygon%3E%3C%2Fsvg%3E');
}

#FORM10
    .ladi-form
    [data-form-checkout-item='product']
    .ladi-form-checkout-payment-check.multiple.checked:before,
#FORM10
    .ladi-form
    [data-form-checkout-item='product']
    .ladi-form-checkout-payment-check:not(.multiple).checked:before,
#FORM10
    .ladi-form
    [data-form-checkout-item='payment']
    .ladi-form-checkout-payment-check.checked:before,
#FORM10
    .ladi-form
    [data-form-checkout-item='fee_shipping']
    .ladi-form-checkout-payment-check.checked:before,
#FORM10 .ladi-form-checkout-payment-quantity .up:before,
#FORM10 .ladi-form-checkout-payment-quantity .down:before,
#FORM10
    [data-form-checkout-item='payment']
    .ladi-form-item
    .ladi-form-checkout-payment-content
    div:before {
    background-color: rgb(43, 43, 43);
}

#FORM10 .ladi-survey-option {
    text-align: left;
}

#FORM10 .ladi-form-item-container,
#FORM10 .ladi-form-checkout-box,
#FORM10 .ladi-form-checkout-bump-offer-product,
#FORM10 .ladi-form-label-container .ladi-form-label-item {
    border-width: 1px;
    border-radius: 10px;
    border-style: solid;
    border-color: rgb(241, 243, 244);
}

#FORM10 .ladi-form-item-container .ladi-form-item.ladi-form-quantity {
    width: calc(100% + 1px);
}

#FORM10 .ladi-form-item-container .ladi-form-quantity .button,
#FORM10
    .ladi-form
    [data-form-checkout-item='coupon_code']
    .content-discount-block
    .placeholder
    i {
    background-color: rgb(241, 243, 244);
}

#FORM10 .ladi-form [data-form-checkout-item='total_price'] .space,
#FORM10 .ladi-form-checkout-payment-quantity input {
    border-color: rgb(241, 243, 244);
}

#FORM10 .ladi-form-checkout-payment-quantity input {
    width: 65px;
}

#FORM10 .ladi-form-item-background,
#POPUP2 > .ladi-popup > .ladi-overlay,
#POPUP2 > .ladi-popup > .ladi-popup-background {
    border-radius: 9px;
}

#BUTTON35,
#FORM_ITEM43,
#FORM_ITEM44 {
    height: 59.5px;
}

#BUTTON35 > .ladi-button > .ladi-button-background {
    background-color: rgb(0, 0, 0);
}

#BUTTON35 > .ladi-button {
    border-radius: 10px;
}

#BUTTON_TEXT35 {
    width: 191px;
    top: 15.3px;
    left: 0px;
}

#BUTTON_TEXT35 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-size: 18px;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(241, 243, 244);
    text-align: center;
}

#FORM_ITEM43 .ladi-form-item,
#FORM_ITEM44 .ladi-form-item {
    background-image: none !important;
}

#HEADLINE381 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    font-weight: bold;
    color: rgb(0, 0, 0);
    text-align: center;
}

#ACCORDION_CONTENT24,
#ACCORDION_CONTENT26,
#ACCORDION_CONTENT18,
#ACCORDION_CONTENT20,
#ACCORDION_CONTENT28 {
    display: none !important;
}

#ACCORDION_CONTENT24 > .ladi-frame-bg > .ladi-frame-background,
#ACCORDION_CONTENT26 > .ladi-frame-bg > .ladi-frame-background,
#ACCORDION_CONTENT18 > .ladi-frame-bg > .ladi-frame-background,
#ACCORDION_CONTENT20 > .ladi-frame-bg > .ladi-frame-background,
#ACCORDION_CONTENT28 > .ladi-frame-bg > .ladi-frame-background {
    background-color: rgb(236, 236, 236);
}

#PARAGRAPH96 > .ladi-paragraph,
#PARAGRAPH97 > .ladi-paragraph,
#PARAGRAPH93 > .ladi-paragraph,
#PARAGRAPH94 > .ladi-paragraph,
#PARAGRAPH98 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    line-height: 1.6;
    color: rgb(0, 0, 0);
}

#ACCORDION_MENU25 > .ladi-frame,
#ACCORDION_MENU27 > .ladi-frame,
#ACCORDION_MENU19 > .ladi-frame,
#ACCORDION_MENU21 > .ladi-frame,
#ACCORDION_MENU29 > .ladi-frame {
    border-width: 0px 0px 1px;
    border-style: solid;
    border-color: rgb(222, 118, 0);
}

#HEADLINE386 > .ladi-headline,
#HEADLINE387 > .ladi-headline,
#HEADLINE383 > .ladi-headline,
#HEADLINE384 > .ladi-headline,
#HEADLINE388 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    line-height: 1.4;
    color: rgb(0, 0, 0);
    text-align: left;
}

#SECTION_POPUP {
    height: 0px;
}

#POPUP2 {
    right: 0px;
    bottom: 0px;
    margin: auto;
}

#POPUP2 > .ladi-popup {
    border-width: 1px;
    border-radius: 10px;
    border-style: solid;
    border-color: rgb(222, 118, 0);
}

#BOX128 > .ladi-box {
    border-radius: 0px;
}

#IMAGE225 > .ladi-image > .ladi-image-background {
    background-image: url('https://w.ladicdn.com/57e4d138957904ae180e0544/66d5b72efbfc7078172d0c8d_cs-icon-smiley-20241029085008-1rjcz.webp');
}

#IMAGE225 > .ladi-image {
    filter: hue-rotate(218deg);
}

#IMAGE225.ladi-animation > .ladi-image {
    animation-name: tada;
    animation-delay: 0s;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

#PARAGRAPH16 {
    width: 316px;
}

#PARAGRAPH16 > .ladi-paragraph {
    font-family: VGlrVGrRGlzcGxheSSZWdbGFyLmZg;
    font-size: 22px;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(0, 0, 0);
    text-align: center;
}

#BUTTON33 > .ladi-button > .ladi-button-background {
    background-image: linear-gradient(
        90deg,
        rgb(252, 148, 0),
        rgb(254, 192, 59)
    );
    background-color: initial;
    background-size: initial;
    background-origin: initial;
    background-position: initial;
    background-repeat: initial;
    background-attachment: initial;
}

#BUTTON33 > .ladi-button {
    box-shadow: rgba(0, 0, 0, 0.48) 10px 10px 20px -15px;
}

#BUTTON33.ladi-animation > .ladi-button {
    animation-name: pulse;
    animation-delay: 0s;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

#BUTTON33 > .ladi-button:hover .ladi-button-background {
    background-image: none !important;
    background-color: rgb(255, 255, 255) !important;
    background-size: initial !important;
    background-origin: initial !important;
    background-position: initial !important;
    background-repeat: initial !important;
    background-attachment: initial !important;
    -webkit-background-clip: initial !important;
}

#BUTTON_TEXT33 > .ladi-headline {
    font-family: VGlrVGrRGlzcGxheSCbxkLmZg;
    line-height: 1.6;
    color: rgb(0, 0, 0);
    text-transform: uppercase;
    text-align: center;
}

@media (min-width: 768px) {
    #SECTION43 {
        height: 839.1px;
    }

    #SECTION43 > .ladi-section-background {
        background-image: url('https://w.ladicdn.com/s1440x839/57e4d138957904ae180e0544/bg1-20250911035411-0obxr.png');
    }

    #GROUP313 {
        width: 813.168px;
        height: 575.434px;
        top: 169.5px;
        left: 440.743px;
    }

    #IMAGE227,
    #IMAGE227 > .ladi-image > .ladi-image-background {
        width: 756.982px;
        height: 514.253px;
    }

    #IMAGE227 {
        top: 61.1807px;
        left: 56.1863px;
    }

    #IMAGE227 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s1100x850/57e4d138957904ae180e0544/ic1-20250911063944-x67_x.png');
    }

    #IMAGE262,
    #IMAGE262 > .ladi-image > .ladi-image-background {
        width: 76.2347px;
        height: 71.5788px;
    }

    #IMAGE262 {
        left: 608.4px;
    }

    #IMAGE262 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/icon1-20250911064107-kro6x.png');
    }

    #IMAGE263,
    #IMAGE263 > .ladi-image > .ladi-image-background {
        width: 93.9834px;
        height: 71.5746px;
    }

    #IMAGE263 {
        top: 452.335px;
    }

    #IMAGE263 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s400x400/57e4d138957904ae180e0544/icon2-20250911064107-gqdyi.png');
    }

    #GROUP270,
    #BOX130,
    #GROUP314,
    #BOX167 {
        width: 279.844px;
        height: 65.5px;
    }

    #GROUP270 {
        top: 685px;
        left: 0px;
    }

    #BUTTON34,
    #BUTTON38 {
        width: 263.918px;
        height: 52.4px;
        top: 6.55px;
        left: 7.96307px;
    }

    #BUTTON_TEXT34,
    #BUTTON_TEXT38 {
        top: 8.13103px;
    }

    #BUTTON_TEXT34 > .ladi-headline,
    #BUTTON_TEXT38 > .ladi-headline,
    #PARAGRAPH76 > .ladi-paragraph,
    #HEADLINE386 > .ladi-headline,
    #HEADLINE387 > .ladi-headline,
    #HEADLINE383 > .ladi-headline,
    #HEADLINE384 > .ladi-headline,
    #HEADLINE388 > .ladi-headline,
    #PARAGRAPH17 > .ladi-paragraph {
        font-size: 20px;
    }

    #GROUP268,
    #BOX129 {
        width: 138px;
        height: 39.25px;
    }

    #GROUP268 {
        top: 68px;
        left: 0px;
    }

    #PARAGRAPH19 {
        width: 127px;
        top: 5.125px;
        left: 5.5px;
    }

    #PARAGRAPH19 > .ladi-paragraph,
    #PARAGRAPH96 > .ladi-paragraph,
    #PARAGRAPH97 > .ladi-paragraph,
    #PARAGRAPH93 > .ladi-paragraph,
    #PARAGRAPH94 > .ladi-paragraph,
    #PARAGRAPH98 > .ladi-paragraph {
        font-size: 18px;
    }

    #GROUP319 {
        width: 732px;
        height: 205px;
        top: 134.5px;
    }

    #HEADLINE374,
    #HEADLINE389 {
        width: 732px;
    }

    #HEADLINE374 > .ladi-headline {
        font-size: 65px;
        text-align: left;
    }

    #HEADLINE389,
    #ACCORDION_CONTENT24,
    #ACCORDION_CONTENT26,
    #ACCORDION_CONTENT18,
    #ACCORDION_CONTENT20,
    #ACCORDION_CONTENT28 {
        top: 72px;
    }

    #HEADLINE389 > .ladi-headline {
        font-size: 102px;
        text-align: left;
    }

    #LIST_PARAGRAPH82,
    #LIST_PARAGRAPH84,
    #LIST_PARAGRAPH85 {
        width: 469px;
    }

    #LIST_PARAGRAPH82 {
        top: 347.934px;
        left: 0px;
    }

    #LIST_PARAGRAPH82 > .ladi-list-paragraph,
    #LIST_PARAGRAPH84 > .ladi-list-paragraph,
    #LIST_PARAGRAPH85 > .ladi-list-paragraph,
    #LIST_PARAGRAPH86 > .ladi-list-paragraph {
        font-size: 20px;
        line-height: 1.6;
    }

    #LIST_PARAGRAPH84 {
        top: 421.267px;
        left: 0px;
    }

    #LIST_PARAGRAPH85 {
        top: 494.601px;
        left: 0px;
    }

    #LIST_PARAGRAPH86 {
        width: 444px;
        top: 567.934px;
        left: 0px;
    }

    #SECTION46 {
        height: 965.1px;
    }

    #HEADLINE377,
    #HEADLINE378,
    #HEADLINE379 {
        width: 745px;
    }

    #HEADLINE377,
    #HEADLINE379,
    #HEADLINE381 {
        top: 108.4px;
        left: 0px;
    }

    #HEADLINE377 > .ladi-headline {
        font-size: 40px;
        line-height: 1.4;
        text-align: left;
    }

    #LINE29,
    #LINE30,
    #LINE32,
    #LINE35 {
        width: 167px;
    }

    #LINE29,
    #LINE32 {
        top: 238.9px;
        left: 0px;
    }

    #LINE31 {
        width: 1965px;
        top: 948.1px;
        left: -498px;
    }

    #GROUP316 {
        width: 1200px;
        height: 536px;
        top: 310.4px;
    }

    #GROUP273 {
        left: 411.676px;
    }

    #GROUP274 {
        left: 823.353px;
    }

    #GROUP275 {
        top: 286px;
        left: 0px;
    }

    #GROUP276 {
        top: 286px;
        left: 411.676px;
    }

    #GROUP277 {
        top: 286px;
        left: 823.353px;
    }

    #SHAPE34 {
        top: 0px;
        left: 0px;
        display: none !important;
    }

    #hethong {
        height: 1646.1px;
    }

    #HEADLINE378 {
        top: 108.4px;
    }

    #HEADLINE378 > .ladi-headline,
    #HEADLINE379 > .ladi-headline,
    #PARAGRAPH74 > .ladi-paragraph {
        font-size: 40px;
        text-align: left;
    }

    #LINE30 {
        top: 181.9px;
        left: 0px;
    }

    #GROUP314 {
        top: 1519.05px;
        left: 460.078px;
    }

    #BOX139,
    #BOX146,
    #BOX147 {
        width: 1200px;
        height: 370px;
    }

    #BOX139 {
        top: 248.4px;
        left: 0px;
    }

    #IMAGE240,
    #IMAGE240 > .ladi-image > .ladi-image-background,
    #IMAGE243,
    #IMAGE243 > .ladi-image > .ladi-image-background {
        width: 516.521px;
        height: 370px;
    }

    #IMAGE240 {
        top: 248.4px;
        left: 643.479px;
    }

    #IMAGE240 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s850x700/57e4d138957904ae180e0544/ic3-20250911052250-lejgn.png');
    }

    #PARAGRAPH34,
    #PARAGRAPH47,
    #LIST_PARAGRAPH80,
    #PARAGRAPH48 {
        width: 554px;
    }

    #PARAGRAPH34 {
        top: 310.2px;
        left: 61.324px;
    }

    #PARAGRAPH34 > .ladi-paragraph,
    #PARAGRAPH47 > .ladi-paragraph,
    #PARAGRAPH48 > .ladi-paragraph {
        font-size: 40px;
    }

    #LIST_PARAGRAPH78,
    #LIST_PARAGRAPH81 {
        width: 671px;
    }

    #LIST_PARAGRAPH78 {
        top: 380.6px;
        left: 61.324px;
    }

    #LIST_PARAGRAPH78 > .ladi-list-paragraph,
    #LIST_PARAGRAPH80 > .ladi-list-paragraph,
    #LIST_PARAGRAPH81 > .ladi-list-paragraph {
        font-size: 18px;
        line-height: 1.6;
    }

    #BOX146 {
        top: 665.9px;
        left: 0px;
    }

    #IMAGE242,
    #IMAGE242 > .ladi-image > .ladi-image-background {
        width: 516.521px;
        height: 370.001px;
    }

    #IMAGE242 {
        top: 665.9px;
        left: 55.479px;
    }

    #IMAGE242 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s850x700/57e4d138957904ae180e0544/ic4-20250911052250-tgr-s.png');
    }

    #PARAGRAPH47 {
        top: 727.7px;
        left: 636px;
    }

    #LIST_PARAGRAPH80 {
        top: 798.1px;
        left: 636px;
    }

    #BOX147 {
        top: 1083.4px;
        left: 0px;
    }

    #IMAGE243 {
        top: 1083.4px;
        left: 643.479px;
    }

    #IMAGE243 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s850x700/57e4d138957904ae180e0544/ic2-20250911052250-ryv3g.png');
    }

    #PARAGRAPH48 {
        top: 1145.2px;
        left: 61.324px;
    }

    #LIST_PARAGRAPH81 {
        top: 1215.6px;
        left: 61.324px;
    }

    #SECTION48 {
        height: 732.1px;
    }

    #GROUP301,
    #BOX148 {
        height: 378px;
    }

    #GROUP301 {
        top: 310.4px;
        left: 0px;
    }

    #PARAGRAPH61 {
        top: 222.5px;
    }

    #GROUP302,
    #BOX156 {
        height: 379px;
    }

    #GROUP302 {
        top: 310.4px;
        left: 411.676px;
    }

    #GROUP303,
    #BOX157 {
        height: 380px;
    }

    #GROUP303 {
        top: 310.4px;
        left: 823.353px;
    }

    #dangky {
        height: 562.1px;
    }

    #BOX158 {
        width: 1200px;
        height: 398px;
        top: 59.4px;
        left: 0px;
    }

    #IMAGE257,
    #IMAGE257 > .ladi-image > .ladi-image-background {
        width: 420.355px;
        height: 425.4px;
    }

    #IMAGE257 {
        top: 0px;
        left: 726.451px;
    }

    #IMAGE257 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s750x750/57e4d138957904ae180e0544/ic5-20250911055154-d86bo.png');
    }

    #PARAGRAPH76 {
        width: 576px;
        top: 243.9px;
        left: 69.324px;
    }

    #PARAGRAPH74 {
        width: 665px;
        top: 116.4px;
        left: 69.324px;
    }

    #FORM10 {
        width: 623px;
        height: 59.5px;
        top: 317.15px;
        left: 69.324px;
    }

    #BUTTON35 {
        width: 127.47px;
        top: 0px;
        left: 495.53px;
    }

    #FORM_ITEM43,
    #FORM_ITEM44 {
        width: 227.927px;
    }

    #FORM_ITEM44 {
        top: 0px;
        left: 247.765px;
    }

    #SECTION50 {
        height: 807.1px;
    }

    #HEADLINE381 {
        width: 1200px;
    }

    #HEADLINE381 > .ladi-headline {
        font-size: 40px;
        line-height: 1.4;
    }

    #LINE35 {
        top: 182.9px;
        left: 516.5px;
    }

    #ACCORDION9,
    #ACCORDION_CONTENT24,
    #ACCORDION_MENU25,
    #ACCORDION10,
    #ACCORDION_CONTENT26,
    #ACCORDION_MENU27,
    #ACCORDION6,
    #ACCORDION_CONTENT18,
    #ACCORDION_MENU19,
    #ACCORDION7,
    #ACCORDION_CONTENT20,
    #ACCORDION_MENU21,
    #ACCORDION11,
    #ACCORDION_CONTENT28,
    #ACCORDION_MENU29 {
        width: 960px;
        height: 65px;
    }

    #ACCORDION9 {
        top: 435px;
        left: 120px;
    }

    #PARAGRAPH96,
    #PARAGRAPH97,
    #PARAGRAPH93,
    #PARAGRAPH94,
    #PARAGRAPH98 {
        width: 877px;
        top: 18px;
        left: 20.3252px;
    }

    #ACCORDION_SHAPE31,
    #ACCORDION_SHAPE31 > .ladi-shape,
    #ACCORDION_SHAPE32,
    #ACCORDION_SHAPE32 > .ladi-shape,
    #ACCORDION_SHAPE28,
    #ACCORDION_SHAPE28 > .ladi-shape,
    #ACCORDION_SHAPE29,
    #ACCORDION_SHAPE29 > .ladi-shape,
    #ACCORDION_SHAPE33,
    #ACCORDION_SHAPE33 > .ladi-shape {
        top: 16px;
        left: 898.995px;
    }

    #ACCORDION_SHAPE31 > .ladi-shape,
    #ACCORDION_SHAPE32 > .ladi-shape,
    #ACCORDION_SHAPE28 > .ladi-shape,
    #ACCORDION_SHAPE29 > .ladi-shape,
    #ACCORDION_SHAPE33 > .ladi-shape {
        width: 32.7722px;
        height: 33px;
    }

    #HEADLINE386,
    #HEADLINE387,
    #HEADLINE383,
    #HEADLINE384,
    #HEADLINE388 {
        width: 783px;
        top: 21px;
        left: 21.5px;
    }

    #ACCORDION10 {
        top: 534.75px;
        left: 120px;
    }

    #ACCORDION6 {
        top: 235.5px;
        left: 120px;
    }

    #ACCORDION7 {
        top: 335.25px;
        left: 120px;
    }

    #ACCORDION11 {
        top: 634.5px;
        left: 120px;
    }

    #POPUP2 {
        width: 570px;
        height: 407px;
    }

    #BOX128 {
        width: 557px;
        height: 212px;
        top: 187px;
        left: 6.4995px;
    }

    #IMAGE225,
    #IMAGE225 > .ladi-image > .ladi-image-background {
        width: 137.157px;
        height: 137.5px;
    }

    #IMAGE225 {
        left: 216.421px;
    }

    #PARAGRAPH16 {
        top: 137.5px;
        left: 126.999px;
    }

    #PARAGRAPH17 {
        width: 542px;
        top: 207px;
        left: 13.999px;
    }

    #BUTTON33 {
        width: 468.296px;
        height: 65.1px;
        top: 296px;
        left: 50.851px;
    }

    #BUTTON_TEXT33 {
        width: 468px;
        top: 9px;
    }

    #BUTTON_TEXT33 > .ladi-headline {
        font-size: 26px;
    }
}

@media (max-width: 767px) {
    #SECTION43 {
        height: 973.37px;
    }

    #SECTION43 > .ladi-section-background {
        background-image: url('https://w.ladicdn.com/s768x973/57e4d138957904ae180e0544/bg2-20250911072322-rkwoe.png');
    }

    #GROUP313 {
        width: 420px;
        height: 297.211px;
        top: 616.5px;
        left: 0px;
    }

    #IMAGE227,
    #IMAGE227 > .ladi-image > .ladi-image-background {
        width: 390.979px;
        height: 265.612px;
    }

    #IMAGE227 {
        top: 31.5998px;
        left: 29.0201px;
    }

    #IMAGE227 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s700x600/57e4d138957904ae180e0544/ic1-20250911063944-x67_x.png');
    }

    #IMAGE262,
    #IMAGE262 > .ladi-image > .ladi-image-background {
        width: 39.3751px;
        height: 36.9704px;
    }

    #IMAGE262 {
        left: 314.237px;
    }

    #IMAGE262 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s350x350/57e4d138957904ae180e0544/icon1-20250911064107-kro6x.png');
    }

    #IMAGE263,
    #IMAGE263 > .ladi-image > .ladi-image-background {
        width: 48.5423px;
        height: 36.9682px;
    }

    #IMAGE263 {
        top: 233.631px;
    }

    #IMAGE263 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s350x350/57e4d138957904ae180e0544/icon2-20250911064107-gqdyi.png');
    }

    #GROUP270,
    #BOX130,
    #GROUP314,
    #BOX167 {
        width: 237.12px;
        height: 55.5px;
    }

    #GROUP270 {
        top: 533.184px;
        left: 91.44px;
    }

    #BUTTON34,
    #BUTTON38 {
        width: 223.625px;
        height: 44.4px;
        top: 5.55px;
        left: 6.74734px;
    }

    #BUTTON_TEXT34,
    #BUTTON_TEXT38 {
        top: 6.88965px;
    }

    #BUTTON_TEXT34 > .ladi-headline,
    #BUTTON_TEXT38 > .ladi-headline {
        font-size: 18px;
    }

    #GROUP268,
    #BOX129 {
        width: 107.911px;
        height: 30.6921px;
    }

    #GROUP268 {
        top: 60px;
        left: 156.044px;
    }

    #PARAGRAPH19 {
        width: 99px;
        top: 2.34605px;
        left: 4.4555px;
    }

    #PARAGRAPH19 > .ladi-paragraph,
    #HEADLINE386 > .ladi-headline,
    #HEADLINE387 > .ladi-headline,
    #HEADLINE383 > .ladi-headline,
    #HEADLINE384 > .ladi-headline,
    #HEADLINE388 > .ladi-headline,
    #PARAGRAPH17 > .ladi-paragraph {
        font-size: 16px;
    }

    #GROUP319 {
        width: 420px;
        height: 119px;
        top: 104.745px;
    }

    #HEADLINE374,
    #HEADLINE389,
    #LINE31,
    #HEADLINE378 {
        width: 420px;
    }

    #HEADLINE374 > .ladi-headline {
        font-size: 36px;
        text-align: center;
    }

    #HEADLINE389 {
        top: 41px;
    }

    #HEADLINE389 > .ladi-headline {
        font-size: 60px;
        text-align: center;
    }

    #LIST_PARAGRAPH82,
    #LIST_PARAGRAPH84,
    #LIST_PARAGRAPH85,
    #LIST_PARAGRAPH86 {
        width: 393px;
    }

    #LIST_PARAGRAPH82 {
        top: 242.745px;
        left: 17.4995px;
    }

    #LIST_PARAGRAPH82 > .ladi-list-paragraph,
    #LIST_PARAGRAPH84 > .ladi-list-paragraph,
    #LIST_PARAGRAPH85 > .ladi-list-paragraph,
    #LIST_PARAGRAPH86 > .ladi-list-paragraph {
        font-size: 18px;
        line-height: 1.4;
    }

    #LIST_PARAGRAPH84 {
        top: 312.412px;
        left: 17.4995px;
    }

    #LIST_PARAGRAPH85 {
        top: 382.078px;
        left: 17.4995px;
    }

    #LIST_PARAGRAPH86 {
        top: 451.745px;
        left: 17.4995px;
    }

    #SECTION46 {
        height: 514px;
    }

    #HEADLINE377,
    #HEADLINE379 {
        width: 400px;
    }

    #HEADLINE377 {
        top: 47px;
        left: 10px;
    }

    #HEADLINE377 > .ladi-headline {
        font-size: 30px;
        line-height: 1.3;
        text-align: center;
    }

    #LINE29,
    #LINE30,
    #LINE32,
    #LINE35 {
        width: 60px;
    }

    #LINE29 {
        top: 176px;
        left: 180px;
    }

    #LINE31 {
        top: 496px;
        left: 0px;
    }

    #GROUP316 {
        width: 2286.97px;
        height: 250px;
        top: 211px;
    }

    #GROUP273 {
        left: 382.064px;
    }

    #GROUP274 {
        left: 764.128px;
    }

    #GROUP275 {
        top: 0px;
        left: 1146.19px;
    }

    #GROUP276 {
        top: 0px;
        left: 1528.26px;
    }

    #GROUP277 {
        top: 0px;
        left: 1910.32px;
    }

    #SHAPE34 {
        top: 461px;
        left: 370px;
    }

    #hethong {
        height: 1619.5px;
    }

    #HEADLINE378 {
        top: 29px;
    }

    #HEADLINE378 > .ladi-headline,
    #HEADLINE379 > .ladi-headline,
    #PARAGRAPH74 > .ladi-paragraph {
        font-size: 30px;
        text-align: center;
    }

    #LINE30 {
        top: 78px;
        left: 180px;
    }

    #GROUP314 {
        top: 1509px;
        left: 91.44px;
    }

    #BOX139 {
        width: 401px;
        height: 497px;
        top: 119px;
        left: 9.5px;
    }

    #IMAGE240,
    #IMAGE240 > .ladi-image > .ladi-image-background,
    #IMAGE242,
    #IMAGE242 > .ladi-image > .ladi-image-background,
    #IMAGE243,
    #IMAGE243 > .ladi-image > .ladi-image-background {
        width: 221.964px;
        height: 159px;
    }

    #IMAGE240 {
        top: 137px;
        left: 99.018px;
    }

    #IMAGE240 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s550x500/57e4d138957904ae180e0544/ic3-20250911052250-lejgn.png');
    }

    #PARAGRAPH34 {
        width: 367px;
        top: 296px;
        left: 21px;
    }

    #PARAGRAPH34 > .ladi-paragraph,
    #PARAGRAPH47 > .ladi-paragraph,
    #PARAGRAPH48 > .ladi-paragraph {
        font-size: 30px;
    }

    #LIST_PARAGRAPH78,
    #PARAGRAPH47,
    #LIST_PARAGRAPH80,
    #PARAGRAPH48,
    #LIST_PARAGRAPH81 {
        width: 378px;
    }

    #LIST_PARAGRAPH78 {
        top: 352px;
        left: 21px;
    }

    #LIST_PARAGRAPH78 > .ladi-list-paragraph,
    #LIST_PARAGRAPH80 > .ladi-list-paragraph,
    #LIST_PARAGRAPH81 > .ladi-list-paragraph {
        font-size: 16px;
        line-height: 1.4;
    }

    #BOX146 {
        width: 401px;
        height: 401px;
        top: 640px;
        left: 9.5px;
    }

    #IMAGE242 {
        top: 653px;
        left: 99.018px;
    }

    #IMAGE242 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s550x500/57e4d138957904ae180e0544/ic4-20250911052250-tgr-s.png');
    }

    #PARAGRAPH47 {
        top: 812px;
        left: 21px;
    }

    #LIST_PARAGRAPH80 {
        top: 866.4px;
        left: 21px;
    }

    #BOX147 {
        width: 401px;
        height: 407px;
        top: 1065px;
        left: 9.5px;
    }

    #IMAGE243 {
        top: 1077px;
        left: 99.018px;
    }

    #IMAGE243 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s550x500/57e4d138957904ae180e0544/ic2-20250911052250-ryv3g.png');
    }

    #PARAGRAPH48 {
        top: 1236px;
        left: 21px;
    }

    #LIST_PARAGRAPH81 {
        top: 1292px;
        left: 21px;
    }

    #SECTION48 {
        height: 1351px;
    }

    #HEADLINE379 {
        top: 27px;
        left: 10px;
    }

    #LINE32 {
        top: 160px;
        left: 180px;
    }

    #GROUP301,
    #BOX148,
    #GROUP303,
    #BOX157 {
        height: 358px;
    }

    #GROUP301 {
        top: 201px;
        left: 21.676px;
    }

    #PARAGRAPH61 {
        top: 217.5px;
    }

    #GROUP302,
    #BOX156 {
        height: 377px;
    }

    #GROUP302 {
        top: 949px;
        left: 21.676px;
    }

    #GROUP303 {
        top: 575px;
        left: 21.676px;
    }

    #dangky {
        height: 612.05px;
    }

    #BOX158 {
        width: 376.647px;
        height: 528px;
        top: 39.8007px;
        left: 21.676px;
    }

    #IMAGE257,
    #IMAGE257 > .ladi-image > .ladi-image-background {
        width: 115.169px;
        height: 116.552px;
    }

    #IMAGE257 {
        top: 14.4989px;
        left: 304.831px;
    }

    #IMAGE257 > .ladi-image > .ladi-image-background {
        background-image: url('https://w.ladicdn.com/s450x450/57e4d138957904ae180e0544/ic5-20250911055154-d86bo.png');
    }

    #PARAGRAPH76,
    #FORM_ITEM43,
    #FORM_ITEM44 {
        width: 341px;
    }

    #PARAGRAPH76 {
        top: 242.301px;
        left: 39.5px;
    }

    #PARAGRAPH76 > .ladi-paragraph {
        font-size: 18px;
        text-align: center;
    }

    #PARAGRAPH74 {
        width: 294px;
        top: 63.801px;
        left: 63px;
    }

    #FORM10 {
        width: 341px;
        height: 207.5px;
        top: 320.551px;
        left: 39.4995px;
    }

    #BUTTON35 {
        width: 190.707px;
        top: 148px;
        left: 75.1465px;
    }

    #FORM_ITEM44 {
        top: 72px;
        left: 0px;
    }

    #SECTION50 {
        height: 617px;
    }

    #HEADLINE381 {
        width: 352px;
        top: 25px;
        left: 34px;
    }

    #HEADLINE381 > .ladi-headline {
        font-size: 30px;
        line-height: 1.2;
    }

    #LINE35 {
        top: 105px;
        left: 180px;
    }

    #ACCORDION9,
    #ACCORDION_MENU25,
    #ACCORDION10,
    #ACCORDION_MENU27,
    #ACCORDION6,
    #ACCORDION_MENU19,
    #ACCORDION7,
    #ACCORDION_MENU21,
    #ACCORDION11,
    #ACCORDION_MENU29 {
        width: 410.5px;
        height: 67px;
    }

    #ACCORDION9 {
        top: 317.5px;
        left: 4.75px;
    }

    #ACCORDION_CONTENT24,
    #ACCORDION_CONTENT26,
    #ACCORDION_CONTENT18,
    #ACCORDION_CONTENT20,
    #ACCORDION_CONTENT28 {
        width: 410.5px;
        height: 74px;
        top: 76px;
    }

    #PARAGRAPH96,
    #PARAGRAPH97,
    #PARAGRAPH93,
    #PARAGRAPH94,
    #PARAGRAPH98 {
        width: 382px;
        top: 11px;
        left: 14.25px;
    }

    #PARAGRAPH96 > .ladi-paragraph,
    #PARAGRAPH97 > .ladi-paragraph,
    #PARAGRAPH93 > .ladi-paragraph,
    #PARAGRAPH94 > .ladi-paragraph,
    #PARAGRAPH98 > .ladi-paragraph {
        font-size: 15px;
    }

    #ACCORDION_SHAPE31,
    #ACCORDION_SHAPE31 > .ladi-shape,
    #ACCORDION_SHAPE32,
    #ACCORDION_SHAPE32 > .ladi-shape,
    #ACCORDION_SHAPE28,
    #ACCORDION_SHAPE28 > .ladi-shape {
        top: 21.5px;
        left: 374px;
    }

    #ACCORDION_SHAPE31 > .ladi-shape,
    #ACCORDION_SHAPE32 > .ladi-shape,
    #ACCORDION_SHAPE28 > .ladi-shape,
    #ACCORDION_SHAPE29 > .ladi-shape,
    #ACCORDION_SHAPE33 > .ladi-shape {
        width: 24px;
        height: 24px;
    }

    #HEADLINE386,
    #HEADLINE387,
    #HEADLINE383,
    #HEADLINE384,
    #HEADLINE388 {
        width: 344px;
    }

    #HEADLINE386,
    #HEADLINE387,
    #HEADLINE383 {
        top: 11px;
        left: 11px;
    }

    #ACCORDION10 {
        top: 405.25px;
        left: 4.75px;
    }

    #ACCORDION6 {
        top: 142px;
        left: 4.75px;
    }

    #ACCORDION7 {
        top: 229.75px;
        left: 4.75px;
    }

    #ACCORDION_SHAPE29,
    #ACCORDION_SHAPE29 > .ladi-shape,
    #ACCORDION_SHAPE33,
    #ACCORDION_SHAPE33 > .ladi-shape {
        top: 11px;
        left: 374px;
    }

    #HEADLINE384,
    #HEADLINE388 {
        top: 12px;
        left: 11px;
    }

    #ACCORDION11 {
        top: 493px;
        left: 4.75px;
    }

    #POPUP2 {
        width: 410.231px;
        height: 357px;
    }

    #BOX128 {
        width: 401px;
        height: 188px;
        top: 162.5px;
        left: 3.5px;
    }

    #IMAGE225,
    #IMAGE225 > .ladi-image > .ladi-image-background {
        width: 120.199px;
        height: 120.5px;
    }

    #IMAGE225 {
        left: 145.016px;
    }

    #PARAGRAPH16 {
        top: 112.5px;
        left: 47.1155px;
    }

    #PARAGRAPH17 {
        width: 366px;
        top: 182.5px;
        left: 21px;
    }

    #BUTTON33 {
        width: 346.813px;
        height: 48.9434px;
        top: 263px;
        left: 30.5935px;
    }

    #BUTTON_TEXT33 {
        width: 347px;
        top: 3.4484px;
    }

    #BUTTON_TEXT33 > .ladi-headline {
        font-size: 20px;
    }
}
body.lazyload .ladi-overlay,
body.lazyload .ladi-box,
body.lazyload .ladi-button-background,
body.lazyload .ladi-collection-item:before,
body.lazyload .ladi-countdown-background,
body.lazyload .ladi-form-item-background,
body.lazyload .ladi-form-label-container .ladi-form-label-item.image,
body.lazyload .ladi-frame-background,
body.lazyload .ladi-gallery-view-item,
body.lazyload .ladi-gallery-control-item,
body.lazyload .ladi-headline,
body.lazyload .ladi-image-background,
body.lazyload .ladi-image-compare,
body.lazyload .ladi-list-paragraph ul li:before,
body.lazyload .ladi-section-background,
body.lazyload .ladi-survey-option-background,
body.lazyload .ladi-survey-option-image,
body.lazyload .ladi-tabs-background,
body.lazyload .ladi-video-background,
body.lazyload .ladi-banner,
body.lazyload .ladi-spin-lucky-screen,
body.lazyload .ladi-spin-lucky-start {
    background-image: none !important;
}
@-webkit-keyframes tada {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }

    10%,
    20% {
        -webkit-transform: scale(0.9) rotate(-3deg);
        transform: scale(0.9) rotate(-3deg);
    }

    30%,
    50%,
    70%,
    90% {
        -webkit-transform: scale(1.1) rotate(3deg);
        transform: scale(1.1) rotate(3deg);
    }

    40%,
    60%,
    80% {
        -webkit-transform: scale(1.1) rotate(-3deg);
        transform: scale(1.1) rotate(-3deg);
    }

    100% {
        -webkit-transform: scale(1) rotate(0);
        transform: scale(1) rotate(0);
    }
}

@keyframes tada {
    0% {
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }

    10%,
    20% {
        -webkit-transform: scale(0.9) rotate(-3deg);
        -ms-transform: scale(0.9) rotate(-3deg);
        transform: scale(0.9) rotate(-3deg);
    }

    30%,
    50%,
    70%,
    90% {
        -webkit-transform: scale(1.1) rotate(3deg);
        -ms-transform: scale(1.1) rotate(3deg);
        transform: scale(1.1) rotate(3deg);
    }

    40%,
    60%,
    80% {
        -webkit-transform: scale(1.1) rotate(-3deg);
        -ms-transform: scale(1.1) rotate(-3deg);
        transform: scale(1.1) rotate(-3deg);
    }

    100% {
        -webkit-transform: scale(1) rotate(0);
        -ms-transform: scale(1) rotate(0);
        transform: scale(1) rotate(0);
    }
}

@-webkit-keyframes pulse {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }

    50% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }

    50% {
        -webkit-transform: scale(1.1);
        -ms-transform: scale(1.1);
        transform: scale(1.1);
    }

    100% {
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }
}

@-webkit-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        transform: translateY(20px);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}

@-webkit-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        -ms-transform: translateY(-20px);
        transform: translateY(-20px);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}

@-webkit-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        transform: translateX(-20px);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}

@-webkit-keyframes shake {
    0%,
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translateX(-10px);
        transform: translateX(-10px);
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translateX(10px);
        transform: translateX(10px);
    }
}

@keyframes shake {
    0%,
    100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
