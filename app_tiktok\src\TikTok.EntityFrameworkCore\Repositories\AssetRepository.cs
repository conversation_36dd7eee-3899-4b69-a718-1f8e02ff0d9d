using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// Repository implementation cho Asset
    /// </summary>
    public class AssetRepository : EfCoreRepository<TikTokDbContext, RawAssetEntity, Guid>, IAssetRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="dbContextProvider">DbContext provider</param>
        public AssetRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        /// <summary>
        /// L<PERSON>y danh sách tài sản theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="includeRemoved">Bao gồm cả tài sản đã bị xóa</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <param name="relationType">Mối quan hệ với BC</param>
        /// <returns>Danh sách tài sản</returns>
        public async Task<List<RawAssetEntity>> GetByBcIdAsync(string bcId, bool includeRemoved = false, AssetType? assetType = null, List<RelationType>? relationTypes = null)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAssets.Where(x => x.BcId == bcId);

            if (assetType.HasValue)
            {
                query = query.Where(x => x.AssetType == assetType.Value);
            }

            if (!relationTypes.IsNullOrEmpty())
            {
                query = query.Where(x => x.RelationType.HasValue &&relationTypes.Contains(x.RelationType.Value));
            }

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.ToListAsync();
        }

        /// <summary>
        /// Lấy tài sản theo Asset ID
        /// </summary>
        /// <param name="assetId">ID của tài sản</param>
        /// <param name="includeRemoved">Bao gồm cả tài sản đã bị xóa</param>
        /// <returns>Tài sản</returns>
        public async Task<RawAssetEntity> GetByAssetIdAsync(string assetId, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAssets.Where(x => x.AssetId == assetId);

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.FirstOrDefaultAsync();
        }

        /// <summary>
        /// Lấy danh sách tài sản với tìm kiếm và phân trang
        /// </summary>
        public async Task<List<RawAssetEntity>> GetListAsync(
            string? sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            string? filter = null,
            string? assetId = null,
            string? assetName = null,
            AssetType? assetType = null,
            string? bcId = null,
            AdAccountType? advertiserAccountType = null,
            AdvertiserRole? advertiserRole = null,
            CatalogRole? catalogRole = null,
            AdCreationEligible? adCreationEligible = null,
            StoreRole? storeRole = null,
            string? ownerBcName = null,
            bool? isRemoved = null,
            DateTime? removedAt = null,
            RelationType? relationType = null,
            RelationStatus? relationStatus = null,
            AdAccountStatus? advertiserStatus = null,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAssets.AsQueryable();

            // Áp dụng các bộ lọc
            if (!string.IsNullOrWhiteSpace(filter))
            {
                query = query.Where(x =>
                    x.AssetId.Contains(filter) ||
                    x.AssetName.Contains(filter) ||
                    x.BcId.Contains(filter) ||
                    x.OwnerBcName.Contains(filter));
            }

            if (!string.IsNullOrWhiteSpace(assetId))
            {
                query = query.Where(x => x.AssetId.Contains(assetId));
            }

            if (!string.IsNullOrWhiteSpace(assetName))
            {
                query = query.Where(x => x.AssetName.Contains(assetName));
            }

            if (assetType.HasValue)
            {
                query = query.Where(x => x.AssetType == assetType.Value);
            }

            if (!string.IsNullOrWhiteSpace(bcId))
            {
                query = query.Where(x => x.BcId.Contains(bcId));
            }

            if (advertiserAccountType.HasValue)
            {
                query = query.Where(x => x.AdvertiserAccountType == advertiserAccountType.Value);
            }

            if (advertiserRole.HasValue)
            {
                query = query.Where(x => x.AdvertiserRole == advertiserRole.Value);
            }

            if (catalogRole.HasValue)
            {
                query = query.Where(x => x.CatalogRole == catalogRole.Value);
            }

            if (adCreationEligible.HasValue)
            {
                query = query.Where(x => x.AdCreationEligible == adCreationEligible.Value);
            }

            if (storeRole.HasValue)
            {
                query = query.Where(x => x.StoreRole == storeRole.Value);
            }

            if (!string.IsNullOrWhiteSpace(ownerBcName))
            {
                query = query.Where(x => x.OwnerBcName.Contains(ownerBcName));
            }

            // Filter by removal status
            if (isRemoved.HasValue)
            {
                query = query.Where(x => x.IsRemoved == isRemoved.Value);
            }
            else
            {
                // By default, exclude removed assets
                query = query.Where(x => !x.IsRemoved);
            }

            // Filter by removal date
            if (removedAt.HasValue)
            {
                query = query.Where(x => x.RemovedAt == removedAt.Value);
            }

            // Filter by relation type
            if (relationType.HasValue)
            {
                query = query.Where(x => x.RelationType == relationType.Value);
            }

            // Filter by relation status
            if (relationStatus.HasValue)
            {
                query = query.Where(x => x.RelationStatus == relationStatus.Value);
            }

            // Filter by advertiser status
            if (advertiserStatus.HasValue)
            {
                query = query.Where(x => x.AdvertiserStatus == advertiserStatus.Value);
            }

            // Apply sorting
            if (!sorting.IsNullOrWhiteSpace())
            {
                query = query.OrderBy(sorting);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreationTime);
            }

            return await query
                .Skip(skipCount)
                .Take(maxResultCount)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy tổng số tài sản với điều kiện tìm kiếm
        /// </summary>
        public async Task<long> GetCountAsync(
            string? filter = null,
            string? assetId = null,
            string? assetName = null,
            AssetType? assetType = null,
            string? bcId = null,
            AdAccountType? advertiserAccountType = null,
            AdvertiserRole? advertiserRole = null,
            CatalogRole? catalogRole = null,
            AdCreationEligible? adCreationEligible = null,
            StoreRole? storeRole = null,
            string? ownerBcName = null,
            bool? isRemoved = null,
            DateTime? removedAt = null,
            RelationType? relationType = null,
            RelationStatus? relationStatus = null,
            AdAccountStatus? advertiserStatus = null,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAssets.AsQueryable();

            // Áp dụng các bộ lọc tương tự như GetListAsync
            if (!string.IsNullOrWhiteSpace(filter))
            {
                query = query.Where(x =>
                    x.AssetId.Contains(filter) ||
                    x.AssetName.Contains(filter) ||
                    x.BcId.Contains(filter) ||
                    x.OwnerBcName.Contains(filter));
            }

            if (!string.IsNullOrWhiteSpace(assetId))
            {
                query = query.Where(x => x.AssetId.Contains(assetId));
            }

            if (!string.IsNullOrWhiteSpace(assetName))
            {
                query = query.Where(x => x.AssetName.Contains(assetName));
            }

            if (assetType.HasValue)
            {
                query = query.Where(x => x.AssetType == assetType.Value);
            }

            if (!string.IsNullOrWhiteSpace(bcId))
            {
                query = query.Where(x => x.BcId.Contains(bcId));
            }

            if (advertiserAccountType.HasValue)
            {
                query = query.Where(x => x.AdvertiserAccountType == advertiserAccountType.Value);
            }

            if (advertiserRole.HasValue)
            {
                query = query.Where(x => x.AdvertiserRole == advertiserRole.Value);
            }

            if (catalogRole.HasValue)
            {
                query = query.Where(x => x.CatalogRole == catalogRole.Value);
            }

            if (adCreationEligible.HasValue)
            {
                query = query.Where(x => x.AdCreationEligible == adCreationEligible.Value);
            }

            if (storeRole.HasValue)
            {
                query = query.Where(x => x.StoreRole == storeRole.Value);
            }

            if (!string.IsNullOrWhiteSpace(ownerBcName))
            {
                query = query.Where(x => x.OwnerBcName.Contains(ownerBcName));
            }

            // Filter by removal status
            if (isRemoved.HasValue)
            {
                query = query.Where(x => x.IsRemoved == isRemoved.Value);
            }
            else
            {
                // By default, exclude removed assets
                query = query.Where(x => !x.IsRemoved);
            }

            // Filter by removal date
            if (removedAt.HasValue)
            {
                query = query.Where(x => x.RemovedAt == removedAt.Value);
            }

            // Filter by relation type
            if (relationType.HasValue)
            {
                query = query.Where(x => x.RelationType == relationType.Value);
            }

            // Filter by relation status
            if (relationStatus.HasValue)
            {
                query = query.Where(x => x.RelationStatus == relationStatus.Value);
            }

            // Filter by advertiser status
            if (advertiserStatus.HasValue)
            {
                query = query.Where(x => x.AdvertiserStatus == advertiserStatus.Value);
            }

            return await query.LongCountAsync(cancellationToken);
        }

        /// <summary>
        /// Kiểm tra Asset ID đã tồn tại chưa
        /// </summary>
        /// <param name="assetId">Asset ID cần kiểm tra</param>
        /// <param name="excludeId">ID cần loại trừ (cho update)</param>
        /// <param name="includeRemoved">Bao gồm cả tài sản đã bị xóa</param>
        /// <returns>True nếu đã tồn tại</returns>
        public async Task<bool> IsAssetIdExistsAsync(string assetId, Guid? excludeId = null, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAssets.Where(x => x.AssetId == assetId);

            if (excludeId.HasValue)
            {
                query = query.Where(x => x.Id != excludeId.Value);
            }

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// Lấy danh sách tài sản theo Asset IDs
        /// </summary>
        /// <param name="assetIds">Danh sách Asset IDs</param>
        /// <param name="includeRemoved">Bao gồm cả tài sản đã bị xóa</param>
        /// <returns>Danh sách tài sản</returns>
        public async Task<List<RawAssetEntity>> GetManyByAssetIdsAsync(List<string> assetIds, bool includeRemoved = false)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.RawAssets.Where(x => assetIds.Contains(x.AssetId));

            if (!includeRemoved)
            {
                query = query.Where(x => !x.IsRemoved);
            }

            return await query.ToListAsync();
        }

        /// <summary>
        /// Đánh dấu tài sản đã bị xóa khỏi Business Center
        /// </summary>
        /// <param name="assetId">ID của tài sản</param>
        /// <param name="removedAt">Thời gian xóa</param>
        /// <returns>True nếu thành công</returns>
        public async Task<bool> MarkAsRemovedAsync(string assetId, DateTime? removedAt = null)
        {
            var dbContext = await GetDbContextAsync();
            var asset = await dbContext.RawAssets
                .FirstOrDefaultAsync(x => x.AssetId == assetId && !x.IsRemoved);

            if (asset == null)
            {
                return false;
            }

            asset.IsRemoved = true;
            asset.RemovedAt = removedAt ?? DateTime.UtcNow;

            await dbContext.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Khôi phục tài sản đã bị xóa
        /// </summary>
        /// <param name="assetId">ID của tài sản</param>
        /// <returns>True nếu thành công</returns>
        public async Task<bool> RestoreAsync(string assetId)
        {
            var dbContext = await GetDbContextAsync();
            var asset = await dbContext.RawAssets
                .FirstOrDefaultAsync(x => x.AssetId == assetId && x.IsRemoved);

            if (asset == null)
            {
                return false;
            }

            asset.IsRemoved = false;
            asset.RemovedAt = null;

            await dbContext.SaveChangesAsync();
            return true;
        }
    }
}