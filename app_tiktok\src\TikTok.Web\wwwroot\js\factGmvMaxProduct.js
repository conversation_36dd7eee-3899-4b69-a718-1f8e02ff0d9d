'use strict';

$(function () {
    class TiktokGmvMaxProductPivotTable {
        constructor() {
            this.pivotTableObj = null;
            this.currencyManager = new Currency('product');
            this.currentCurrency = this.currencyManager.getCurrentCurrency();
            this.availableCurrencies = ['USD', 'VND'];

            this.pendingRefresh = false;
            this.refreshTimeout = null;

            this.cachedLookups = null;
            this.lastDataHash = null;

            this.performanceMetrics = {
                refreshCount: 0,
                dataProcessingTime: 0,
                lastRefreshTime: null,
                totalProcessedRecords: 0,
            };

            this.dataBoundTimeout = null;

            // Debounce timer for insights updates
            this._insightsTimer = null;
            this._insightsDebounceMs = 500;
            // Debounce timer for conditional formatting
            this._formattingTimer = null;
            this._formattingDebounceMs = 300;

            // Get alert thresholds from window.ProductAlertThresholds
            this.alertThresholds = window.ProductAlertThresholds || {
                roasCritical: 1.5, // ROAS < 1.5
                roasLow: 2.0, // ROAS < 2.0
                roasGood: 3.0, // ROAS > 3.0
                tacosHigh: 30, // TACOS > 30%
                tacosMedium: 20, // TACOS > 20%
                lowSalesThreshold: 5, // Less than 5 units sold
                highCostPerOrderThreshold: 100, // Cost per order > $100
            };

            // ✅ Heatmap thresholds for product performance
            this.heatmapThresholds = {
                // ROAS thresholds
                roasEmergency: 1.0, // Dark red - product losing money
                roasCritical: this.alertThresholds.roasCritical, // Red - critical ROAS
                roasLow: this.alertThresholds.roasLow, // Orange - low ROAS
                roasGood: this.alertThresholds.roasGood, // Green - good ROAS
                roasExcellent: 5.0, // Dark green - excellent ROAS
                // Colors for heatmap visualization
                colors: {
                    emergency: { bg: '#d32f2f', color: '#ffffff' }, // Dark red
                    critical: { bg: '#f44336', color: '#ffffff' }, // Red
                    low: { bg: '#ff9800', color: '#ffffff' }, // Orange
                    warning: { bg: '#ffc107', color: '#212121' }, // Yellow
                    good: { bg: '#4caf50', color: '#ffffff' }, // Green
                    excellent: { bg: '#2e7d32', color: '#ffffff' }, // Dark green
                    veryHigh: { bg: '#1b5e20', color: '#ffffff' }, // Very dark green
                },
            };
        }

        batchedRefresh(changes = {}) {
            // Clear any pending refresh
            if (this.refreshTimeout) {
                clearTimeout(this.refreshTimeout);
            }

            // Apply changes immediately but defer refresh
            if (changes.values && this.pivotTableObj) {
                this.pivotTableObj.dataSourceSettings.values = changes.values;
            }
            if (changes.dataSource && this.pivotTableObj) {
                this.pivotTableObj.dataSourceSettings.dataSource =
                    changes.dataSource;
            }
            if (changes.formatSettings && this.pivotTableObj) {
                this.pivotTableObj.dataSourceSettings.formatSettings =
                    changes.formatSettings;
            }
            if (changes.conditionalFormatSettings && this.pivotTableObj) {
                this.pivotTableObj.dataSourceSettings.conditionalFormatSettings =
                    changes.conditionalFormatSettings;
            }
            if (changes.filterSettings && this.pivotTableObj) {
                this.pivotTableObj.dataSourceSettings.filterSettings =
                    changes.filterSettings;
            }

            // Batch refresh with small delay to catch multiple rapid changes
            this.refreshTimeout = setTimeout(() => {
                if (this.pivotTableObj && !this.pendingRefresh) {
                    this.pendingRefresh = true;

                    const refreshStart = performance.now();
                    this.performanceMetrics.refreshCount++;
                    this.performanceMetrics.lastRefreshTime = Date.now();

                    this.pivotTableObj.refresh();

                    // Reset flag after refresh completes
                    setTimeout(() => {
                        this.pendingRefresh = false;
                    }, 100);
                }
            }, 50); // 50ms delay to batch multiple rapid changes
        }

        generateDataHash(data) {
            if (!data) return null;

            const factCount = data.factGmvMaxProducts?.length || 0;
            const dimCounts = [
                data.dimAdAccounts?.length || 0,
                data.dimBusinessCenters?.length || 0,
                data.dimCampaigns?.length || 0,
                data.dimStores?.length || 0,
                data.dimProducts?.length || 0,
                data.dimDates?.length || 0,
            ].join('-');

            return `${factCount}_${dimCounts}`;
        }

        createCachedLookups(data) {
            if (!data) return {};

            const dataHash = this.generateDataHash(data);

            // Return cached lookups if data hasn't changed
            if (this.lastDataHash === dataHash && this.cachedLookups) {
                return this.cachedLookups;
            }
            this.cachedLookups = {
                adAccount: this.createLookup(data.dimAdAccounts, 'id'),
                businessCenter: this.createLookup(
                    data.dimBusinessCenters,
                    'id'
                ),
                campaign: this.createLookup(data.dimCampaigns, 'id'),
                store: this.createLookup(data.dimStores, 'id'),
                product: this.createLookup(data.dimProducts, 'id'),
                date: this.createLookup(data.dimDates, 'id'),
            };

            this.lastDataHash = dataHash;
            return this.cachedLookups;
        }

        async initial() {
            const dataSource = await this.extractPivotDataOptimized([
                'product',
            ]);

            this.fullDataSource = dataSource;

            // Get filtered values based on permissions
            const filteredValues =
                await this.getCurrencyValuesWithAggregations();

            this.pivotTableObj = new ej.pivotview.PivotView({
                dataSourceSettings: {
                    dataSource: dataSource,
                    allowLabelFilter: false,
                    allowValueFilter: false,
                    allowMemberFilter: false,
                    enableSorting: true,
                    allowCalculatedField: true,

                    // ✅ Business-oriented row hierarchy: Business Center => Shop => Product
                    rows: [
                        {
                            name: 'BusinessCenterName',
                            caption: 'Trung tâm kinh doanh',
                            showSubTotals: true,
                        },
                        {
                            name: 'StoreName',
                            caption: 'Tên Shop',
                            showSubTotals: true,
                        },
                        {
                            name: 'ProductName',
                            caption: 'Tên sản phẩm',
                            showSubTotals: false,
                        },
                        {
                            name: 'ProductImageUrl',
                            caption: 'Hình ảnh SP',
                            showSubTotals: false,
                        },
                    ],

                    columns: this.getSmartTimeColumns(),

                    // ✅ Product metrics with dynamic currency and per-column aggregation
                    values: filteredValues,

                    // ✅ Filters
                    filters: [],

                    // ✅ Number formatting with currency support
                    formatSettings: this.getCurrencyFormatSettings(),

                    // ✅ Calculated fields for product analysis
                    calculatedFieldSettings: [
                        {
                            name: 'Revenue_Per_Unit',
                            formula:
                                '"Orders" > 0 ? ("GrossRevenue" / "Orders") : 0',
                            caption: 'Doanh thu mỗi sản phẩm',
                        },
                        {
                            name: 'Product_Efficiency_Score',
                            formula:
                                '("ROAS" > 3 && "Orders" > 20) ? 100 : (("ROAS" > 2 && "Orders" > 5) ? 70 : (("ROAS" > 1.5) ? 40 : 20))',
                            caption: 'Điểm hiệu quả sản phẩm',
                        },
                    ],

                    expandAll: true,

                    // ✅ Hide unwanted values by default - USD only
                    excludeFields: [
                        'NetCost',
                        'NetCostVND', // Legacy VND fields
                        'CostPerOrderVND', // Legacy VND fields
                        'CPMVND', // Legacy VND fields
                        'CPCVND', // Legacy VND fields
                        'ProductPriceVND', // Legacy VND fields
                        // Note: Keep USD fields available for calculations
                    ],

                    // ✅ Sort by performance metrics
                    sortSettings: [
                        { name: 'ROAS', order: 'Descending' },
                        { name: 'Orders', order: 'Descending' },
                    ],
                },

                locale:
                    (window.dashboardConfig && window.dashboardConfig.locale) ||
                    'vi-VN',
                height: 1000,
                width: '100%',
                showGroupingBar: false,
                showFieldList: true,
                allowExcelExport: true,
                allowPdfExport: false,
                showToolbar: true,

                showValuesButton: false,
                showRowSubTotals: false,
                showColumnSubTotals: false,
                showGrandTotals: false,
                gridSettings: {
                    layout: 'Tabular',
                    columnWidth: 140,
                    allowSelection: false,
                    selectionSettings: { mode: 'Cell', type: 'Multiple' },
                    // ✅ Performance optimization: Enable virtualization if supported
                    enableVirtualization: true,
                    allowPaging: true,
                    pageSize: 100,
                },

                // ✅ Toolbar for product analysis
                toolbar: [
                    'SubTotal',
                    'GrandTotal',
                    'FieldList',
                    'ConditionalFormatting',
                    'NumberFormatting',
                ],

                // ✅ Product performance heatmap (disabled by default for performance)
                conditionalFormatSettings: [],
                allowConditionalFormatting: true,

                // ✅ Event handlers
                cellClick: (args) => {
                    this.handleCellClick(args);
                },

                dataBound: () => {
                    if (this.dataBoundTimeout) {
                        clearTimeout(this.dataBoundTimeout);
                    }
                    this.dataBoundTimeout = setTimeout(() => {
                        this.updateProductInsights();
                    }, 200); // 200ms debounce
                },

                // Chart settings for product analysis
                chartSettings: {
                    chartSeries: {
                        type: 'Column',
                        animation: { enable: true },
                    },
                    primaryYAxis: {
                        title: 'Doanh thu (USD)',
                        labelFormat: 'C0',
                    },
                },
            });

            // Render to container
            this.pivotTableObj.appendTo('#FactGmvMaxProductPivotTable');

            await this.applyInitialValuesConfiguration();

            setTimeout(() => {
                if (typeof populateFilterOptions === 'function') {
                    populateFilterOptions();
                }
            }, 100); // Reduced from 500ms to 100ms
        }

        async applyInitialValuesConfiguration() {
            // Get saved values from localStorage
            let savedValues = localStorage.getItem('productValueSelectedState');
            let desiredValues;

            if (savedValues) {
                try {
                    desiredValues = JSON.parse(savedValues);
                } catch (e) {
                    console.warn(
                        '⚠️ Error parsing saved values, using defaults:',
                        e
                    );
                    desiredValues = [
                        'GrossRevenue',
                        'Cost',
                        'ROAS',
                        'TACOS',
                        'ProductImpressions',
                        'Orders',
                    ];
                }
            } else {
                desiredValues = [
                    'GrossRevenue',
                    'Cost',
                    'ROAS',
                    'TACOS',
                    'ProductImpressions',
                    'Orders',
                ];
            }

            // Apply values directly to pivot table configuration without triggering refresh
            if (desiredValues && desiredValues.length > 0) {
                const currentAggregations = this.getStoredColumnAggregations();
                const currencyValues = await this.getCurrencyValues(
                    currentAggregations
                );
                const newValues = currencyValues.filter((value) =>
                    desiredValues.includes(value.name)
                );

                this.pivotTableObj.dataSourceSettings.values = newValues;
            } else {
                console.warn(
                    '⚠️ No valid values to apply, keeping default values'
                );
            }
        }

        // ✅ Optimized data extraction with chunking
        async extractPivotDataOptimized(
            includes = ['product'],
            dataToUse = null
        ) {
            let sourceData = dataToUse;

            if (!sourceData) {
                try {
                    // Show loading indicator
                    this.showDataLoadingIndicator();

                    // Check permissions first - wait for ABP to be ready
                    await window.PermissionHelper.waitForABP();
                    const permissions =
                        window.PermissionHelper.getPermissions('product');
                    if (
                        !permissions.viewSpending &&
                        !permissions.viewMetrics &&
                        !permissions.viewAll &&
                        !permissions.viewAllAdvertisers
                    ) {
                        this.hideDataLoadingIndicator();
                        window.PermissionHelper.showPermissionDeniedMessage(
                            'product'
                        );
                        return [];
                    }

                    // Fetch data from API
                    const apiUrl = this.getApiUrl();
                    const response = await fetch(apiUrl);
                    if (!response.ok) {
                        throw new Error(
                            `HTTP error! status: ${response.status}`
                        );
                    }
                    sourceData = await response.json();

                    // Hide loading indicator
                    this.hideDataLoadingIndicator();
                } catch (error) {
                    console.error(
                        '❌ Error fetching product data from API:',
                        error
                    );
                    this.hideDataLoadingIndicator();
                    showToast(
                        'error',
                        'Lỗi tải dữ liệu từ API: ' + error.message
                    );
                    return [];
                }
            }

            if (!sourceData?.factGmvMaxProducts) {
                console.warn('No product data found, using empty dataset');
                return [];
            }

            const facts = sourceData.factGmvMaxProducts;
            if (!facts || facts.length === 0) {
                console.warn('FactGmvMaxProducts array is empty');
                return [];
            }

            const transformedData = [];

            const BATCH_SIZE = 500;

            const lookups = this.createCachedLookups(sourceData);

            const processingStart = performance.now();

            for (let i = 0; i < facts.length; i += BATCH_SIZE) {
                const batch = facts.slice(i, i + BATCH_SIZE);

                const batchResults = batch.map((fact) => {
                    const dateInfo = lookups.date[fact.dimDateId] || {};
                    const adAccountInfo =
                        lookups.adAccount[fact.dimAdAccountId] || {};
                    const businessCenterInfo =
                        lookups.businessCenter[fact.dimBusinessCenterId] || {};
                    const campaignInfo =
                        lookups.campaign[fact.dimCampaignId] || {};
                    const storeInfo = lookups.store[fact.dimStoreId] || {};
                    const productInfo = fact.dimProductId
                        ? lookups.product[fact.dimProductId]
                        : null;

                    const transformedRecord = {
                        // Core product identification
                        ProductId: fact.productId,
                        ProductName:
                            fact.productName ||
                            productInfo.productName ||
                            fact.productId,
                        SKU: fact.sku || productInfo.sku || '',
                        Category:
                            fact.category ||
                            productInfo.category ||
                            'Uncategorized',

                        // Product pricing info
                        ProductPrice: this.getCurrencyValue(
                            fact,
                            'productPrice'
                        ),

                        // Business context
                        BusinessCenterName:
                            businessCenterInfo.bcName || 'Unknown BC',
                        BusinessCenterId: businessCenterInfo.bcId || '',
                        AdAccountName: adAccountInfo.advertiserName || '',
                        AdAccountId: adAccountInfo.advertiserId || '',

                        // Campaign and Store info
                        CampaignName: campaignInfo.campaignName || '',
                        CampaignId: fact.campaignId,
                        StoreName: storeInfo.storeName || 'Unknown Store',
                        StoreId: fact.storeId,

                        // Date context
                        Date: dateInfo.fullDate || fact.date,
                        DateFormatted:
                            dateInfo.dateFormat_DDMMYYYY ||
                            formatDate(fact.date),
                        DateKey: fact.dimDateId,
                        Year:
                            dateInfo.year || new Date(fact.date).getFullYear(),
                        Month:
                            dateInfo.month ||
                            new Date(fact.date).getMonth() + 1,
                        MonthName:
                            dateInfo.monthName || getMonthName(fact.date),
                        WeekDay: getVietnameseWeekday(new Date(fact.date)),
                        WeekOfYear: getWeekOfYear(fact.date),
                        WeekOfMonth: getWeekOfMonth(fact.date),
                        WeekStartDate: getWeekStartDate(fact.date),
                        WeekEndDate: getWeekEndDate(fact.date),
                        Quarter: getQuarter(fact.date),

                        // ✅ New formatted fields for smart grouping
                        WeekMonthYear: getWeekMonthYear(fact.date),
                        MonthYear: getMonthYear(fact.date),
                        YearFormatted: getYearFormatted(fact.date),

                        // Financial metrics with currency support
                        Cost: this.getCurrencyValue(fact, 'cost'),
                        NetCost: this.getCurrencyValue(fact, 'netCost'),
                        GrossRevenue: this.getCurrencyValue(
                            fact,
                            'grossRevenue'
                        ),
                        AdsRevenue: this.getCurrencyValue(fact, 'adsRevenue'),
                        OrganicRevenue: this.getCurrencyValue(
                            fact,
                            'organicRevenue'
                        ),

                        // Performance metrics
                        Orders: fact.orders || 0,
                        QuantitySold: fact.quantitySold || 0,
                        CostPerOrder: this.getCurrencyValue(
                            fact,
                            'costPerOrder'
                        ),
                        ROI: fact.roi || 0,
                        ROAS: fact.roas || 0,
                        ACOS: fact.acos || 0,
                        TACOS: fact.tacos || 0,

                        // Traffic metrics
                        Impressions: fact.impressions || 0,
                        Clicks: fact.clicks || 0,
                        CTR: fact.ctr || 0,
                        CPM: this.getCurrencyValue(fact, 'cpm'),
                        CPC: this.getCurrencyValue(fact, 'cpc'),
                        ConversionRate: fact.conversionRate || 0,

                        Currency: fact.currency || 'USD',
                    };

                    this.calculateProductMetricsInline(transformedRecord);
                    this.addProductClassificationsInline(transformedRecord);

                    return transformedRecord;
                });

                transformedData.push(...batchResults);

                if (i + BATCH_SIZE < facts.length) {
                    await new Promise((resolve) => setTimeout(resolve, 0));
                }
            }

            // ✅ PERFORMANCE: Store processing metrics
            const processingDuration = performance.now() - processingStart;
            this.performanceMetrics.dataProcessingTime = processingDuration;
            this.performanceMetrics.totalProcessedRecords =
                transformedData.length;

            return transformedData;
        }

        // Keep original method for backward compatibility
        async extractPivotData(includes = ['product'], dataToUse = null) {
            return this.extractPivotDataOptimized(includes, dataToUse);
        }

        // ✅ Helper methods for API loading
        getApiUrl() {
            const config = window.dashboardConfig || {};
            const fromDate =
                config.fromDate ||
                new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                    .toISOString()
                    .split('T')[0];
            const toDate =
                config.toDate || new Date().toISOString().split('T')[0];
            const type = config.type || 'product';

            const url = `/api/fact-gmv-max-product/data?fromDate=${fromDate}&toDate=${toDate}&type=${type}`;
            return url;
        }

        showDataLoadingIndicator() {
            const indicator = document.getElementById('data-loading-indicator');
            if (indicator) {
                indicator.style.display = 'block';
            }
        }

        hideDataLoadingIndicator() {
            const indicator = document.getElementById('data-loading-indicator');
            if (indicator) {
                indicator.style.display = 'none';
            }
        }

        // ✅ Error message function removed - using shared common-utils.js instead

        // ✅ Generate product performance heatmap
        generateProductHeatmapFormatting() {
            const formatSettings = [];
            const measures = ['ROAS', 'TACOS', 'QuantitySold'];
            const thresholds = this.heatmapThresholds;

            // ROAS formatting
            formatSettings.push(
                // Emergency ROAS (< 1.0) - Dark Red
                {
                    measure: 'ROAS',
                    value1: 0,
                    value2: thresholds.roasEmergency,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.emergency.bg,
                        color: thresholds.colors.emergency.color,
                        fontWeight: 'bold',
                        border: '2px solid #b71c1c',
                    },
                },
                // Critical ROAS (1.0 - 1.5) - Red
                {
                    measure: 'ROAS',
                    value1: thresholds.roasEmergency,
                    value2: thresholds.roasCritical,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.critical.bg,
                        color: thresholds.colors.critical.color,
                        fontWeight: 'bold',
                    },
                },
                // Low ROAS (1.5 - 2.0) - Orange
                {
                    measure: 'ROAS',
                    value1: thresholds.roasCritical,
                    value2: thresholds.roasLow,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.low.bg,
                        color: thresholds.colors.low.color,
                        fontWeight: '500',
                    },
                },
                // Good ROAS (2.0 - 3.0) - Yellow
                {
                    measure: 'ROAS',
                    value1: thresholds.roasLow,
                    value2: thresholds.roasGood,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.warning.bg,
                        color: thresholds.colors.warning.color,
                    },
                },
                // Excellent ROAS (3.0 - 5.0) - Green
                {
                    measure: 'ROAS',
                    value1: thresholds.roasGood,
                    value2: thresholds.roasExcellent,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.good.bg,
                        color: thresholds.colors.good.color,
                    },
                },
                // Very High ROAS (> 5.0) - Dark Green
                {
                    measure: 'ROAS',
                    value1: thresholds.roasExcellent,
                    conditions: 'GreaterThan',
                    style: {
                        backgroundColor: thresholds.colors.excellent.bg,
                        color: thresholds.colors.excellent.color,
                        fontWeight: 'bold',
                    },
                }
            );

            // TACOS formatting
            formatSettings.push(
                // High TACOS (> 30%) - Red
                {
                    measure: 'TACOS',
                    value1: this.alertThresholds.tacosHigh,
                    conditions: 'GreaterThan',
                    style: {
                        backgroundColor: '#ffebee',
                        color: '#c62828',
                        fontWeight: 'bold',
                    },
                },
                // Medium TACOS (20% - 30%) - Yellow
                {
                    measure: 'TACOS',
                    value1: this.alertThresholds.tacosMedium,
                    value2: this.alertThresholds.tacosHigh,
                    conditions: 'Between',
                    style: {
                        backgroundColor: '#fff8e1',
                        color: '#ef6c00',
                    },
                }
            );

            // Quantity Sold formatting
            formatSettings.push(
                // Low sales (< 5) - Red
                {
                    measure: 'QuantitySold',
                    value1: 0,
                    value2: this.alertThresholds.lowSalesThreshold,
                    conditions: 'Between',
                    style: {
                        backgroundColor: '#ffebee',
                        color: '#c62828',
                        fontWeight: 'bold',
                    },
                },
                // High sales (> 50) - Green
                {
                    measure: 'QuantitySold',
                    value1: 50,
                    conditions: 'GreaterThan',
                    style: {
                        backgroundColor: '#e8f5e8',
                        color: '#2e7d32',
                        fontWeight: 'bold',
                    },
                }
            );

            return formatSettings;
        }

        // ✅ Inline optimized metrics calculation
        calculateProductMetricsInline(record) {
            // ✅ Single-pass calculation for better performance
            const clicks = record.Clicks || 0;
            const impressions = record.Impressions || 0;
            const grossRevenue = record.GrossRevenue || 0;
            const cost = record.Cost || 0;
            const orders = record.Orders || 0;
            const quantitySold = record.QuantitySold || 0;

            // Conversion rate
            record.ConversionRate =
                clicks > 0
                    ? parseFloat(((orders / clicks) * 100).toFixed(2))
                    : 0;

            // Revenue per unit
            record.RevenuePerUnit =
                quantitySold > 0
                    ? parseFloat((grossRevenue / quantitySold).toFixed(2))
                    : 0;

            // Cost per unit
            record.CostPerUnit =
                quantitySold > 0
                    ? parseFloat((cost / quantitySold).toFixed(2))
                    : 0;

            // Profit margin per unit
            record.ProfitMarginPerUnit =
                record.RevenuePerUnit > 0
                    ? parseFloat(
                          (
                              ((record.RevenuePerUnit - record.CostPerUnit) /
                                  record.RevenuePerUnit) *
                              100
                          ).toFixed(2)
                      )
                    : 0;
        }

        // Keep original method for backward compatibility
        calculateProductMetrics(record) {
            this.calculateProductMetricsInline(record);
        }

        // ✅ Inline optimized classifications
        addProductClassificationsInline(record) {
            const roas = record.ROAS || 0;
            const tacosPct = (record.TACOS || 0) * 100;
            const quantitySold = record.QuantitySold || 0;

            // ✅ Inline calculations for better performance
            record.ROASStatus =
                roas >= this.alertThresholds.roasGood
                    ? 'Excellent'
                    : roas >= this.alertThresholds.roasLow
                    ? 'Good'
                    : roas >= this.alertThresholds.roasCritical
                    ? 'Warning'
                    : 'Critical';

            record.ACOSStatus =
                tacosPct >= this.alertThresholds.tacosHigh
                    ? 'High'
                    : tacosPct >= this.alertThresholds.tacosMedium
                    ? 'Medium'
                    : 'Low';

            record.SalesStatus =
                quantitySold >= 50
                    ? 'Hot Seller'
                    : quantitySold >= 10
                    ? 'Regular'
                    : quantitySold >= this.alertThresholds.lowSalesThreshold
                    ? 'Slow'
                    : 'Very Slow';

            record.ProductHealth =
                roas >= 3.0 && tacosPct <= 30 && quantitySold >= 50
                    ? 'Excellent'
                    : roas >= 2.0 && tacosPct <= 40 && quantitySold >= 10
                    ? 'Good'
                    : roas >= 1.5 && quantitySold >= 5
                    ? 'Fair'
                    : 'Poor';

            record.AlertLevel =
                roas < 1.5 ||
                tacosPct > this.alertThresholds.tacosHigh ||
                quantitySold < this.alertThresholds.lowSalesThreshold
                    ? 'High'
                    : roas < 2.0 ||
                      tacosPct > this.alertThresholds.tacosMedium ||
                      quantitySold < 10
                    ? 'Medium'
                    : 'Low';
        }

        // Keep original method for backward compatibility
        addProductClassifications(record) {
            this.addProductClassificationsInline(record);
        }

        // ✅ Currency-aware value getter (align with FactBalance behavior)
        getCurrencyValue(fact, fieldName) {
            if (this.currentCurrency === 'VND') {
                const vndVal = fact[`${fieldName}VND`];
                if (vndVal !== undefined) return vndVal || 0;
            } else {
                const usdVal = fact[`${fieldName}USD`];
                if (usdVal !== undefined) return usdVal || 0;
            }
            const fallback = fact[fieldName];
            return fallback || 0;
        }

        // ✅ Refresh currency from localStorage
        refreshCurrency() {
            if (this.currencyManager) {
                this.currentCurrency =
                    this.currencyManager.getStoredCurrency() || 'USD';
            }
        }

        // ✅ Store currency preference
        storeCurrency(currency) {
            if (this.currencyManager) {
                this.currencyManager.setStoredCurrency(currency);
            }
        }

        getCurrencyFormatSettings() {
            const isVnd = this.currentCurrency === 'VND';
            const suffix = isVnd ? ' đ' : ' $';
            return [
                { name: 'Cost', format: 'N0', suffix },
                { name: 'GrossRevenue', format: 'N0', suffix },
                { name: 'AdsRevenue', format: 'N0', suffix },
                { name: 'ProductPrice', format: 'N2', suffix },
                { name: 'CostPerOrder', format: 'N2', suffix },
                { name: 'ROAS', format: 'N2', suffix: 'x' },
                { name: 'TACOS', format: 'N1', suffix: '%' },
                { name: 'ConversionRate', format: 'N2', suffix: '%' },
            ];
        }

        // ✅ Switch currency and refresh (align with FactBalance)
        async switchCurrency(newCurrency) {
            if (!this.availableCurrencies.includes(newCurrency)) {
                console.warn(`Unsupported currency: ${newCurrency}`);
                return;
            }
            this.currentCurrency = newCurrency;
            // Persist selection like FactBalance
            if (this.currencyManager?.setStoredCurrency) {
                this.currencyManager.setStoredCurrency(newCurrency);
            }

            // Update format settings
            this.pivotTableObj.dataSourceSettings.formatSettings =
                this.getCurrencyFormatSettings();

            // Preserve selected values and current aggregations
            const selectedValues = JSON.parse(
                localStorage.getItem('productValueSelectedState') || '[]'
            );
            const currentAggregations = this.getStoredColumnAggregations();
            let newValues = await this.getCurrencyValues();
            // Apply aggregations
            newValues = newValues.map((v) => ({
                name: v.name,
                caption: v.caption,
                type: currentAggregations[v.name] || v.type || 'Sum',
                showSubTotals: v.showSubTotals,
            }));
            if (Array.isArray(selectedValues) && selectedValues.length > 0) {
                newValues = newValues.filter((v) =>
                    selectedValues.includes(v.name)
                );
            }
            this.pivotTableObj.dataSourceSettings.values = newValues;

            // Re-extract data with new currency
            this.pivotTableObj.dataSourceSettings.dataSource =
                await this.extractPivotData(['product']);
            this.pivotTableObj.refresh();

            // Update heatmap thresholds for currency
            this.updateHeatmapThresholdsForCurrency();
        }

        async getCurrencyValues(columnAggregations = null) {
            // ✅ NEW: Dynamic currency support like FactBalance
            const currency = this.currentCurrency;
            const currencySymbol = currency === 'VND' ? '₫' : '$';
            const aggregations =
                columnAggregations || this.getStoredColumnAggregations();

            // Get permissions to filter fields - wait for ABP to be ready
            await window.PermissionHelper.waitForABP();
            const permissions =
                window.PermissionHelper.getPermissions('product');

            const allValues = [
                // Revenue Metrics
                {
                    name: 'GrossRevenue',
                    caption: `Tổng doanh thu (${currencySymbol})`,
                    type: aggregations['GrossRevenue'] || 'Sum',
                    showSubTotals: true,
                    category: 'metrics',
                },
                {
                    name: 'AdsRevenue',
                    caption: `Doanh thu quảng cáo (${currencySymbol})`,
                    type: aggregations['AdsRevenue'] || 'Sum',
                    showSubTotals: true,
                    category: 'metrics',
                },
                {
                    name: 'OrganicRevenue',
                    caption: `Doanh thu tự nhiên (${currencySymbol})`,
                    type: aggregations['OrganicRevenue'] || 'Sum',
                    showSubTotals: true,
                    category: 'metrics',
                },

                // Cost Metrics
                {
                    name: 'Cost',
                    caption: `Chi phí quảng cáo (${currencySymbol})`,
                    type: aggregations['Cost'] || 'Sum',
                    showSubTotals: true,
                    category: 'spending',
                },
                {
                    name: 'CostPerOrder',
                    caption: `Chi phí mỗi đơn (${currencySymbol})`,
                    type: aggregations['CostPerOrder'] || 'Avg',
                    showSubTotals: false,
                    category: 'spending',
                },

                // Product Specific Metrics
                {
                    name: 'ProductPrice',
                    caption: `Giá sản phẩm (${currencySymbol})`,
                    type: aggregations['ProductPrice'] || 'Avg',
                    showSubTotals: false,
                    category: 'metrics',
                },
                {
                    name: 'QuantitySold',
                    caption: `Số lượng bán được`,
                    type: aggregations['QuantitySold'] || 'Sum',
                    showSubTotals: true,
                    category: 'metrics',
                },

                // Performance Metrics
                {
                    name: 'ROAS',
                    caption: 'ROAS (Tỷ lệ hoàn vốn quảng cáo)',
                    type: aggregations['ROAS'] || 'Avg',
                    showSubTotals: true,
                    category: 'metrics',
                },
                {
                    name: 'TACOS',
                    caption: 'TACOS (Tỷ lệ chi phí quảng cáo trên doanh thu)',
                    type: aggregations['TACOS'] || 'Avg',
                    showSubTotals: true,
                    category: 'restricted',
                },

                // Volume/Traffic Metrics
                {
                    name: 'Orders',
                    caption: 'Số đơn hàng',
                    type: aggregations['Orders'] || 'Sum',
                    showSubTotals: true,
                    category: 'metrics',
                },
                {
                    name: 'ProductImpressions',
                    caption: 'Lượt xem sản phẩm',
                    type: aggregations['ProductImpressions'] || 'Sum',
                    showSubTotals: false,
                    category: 'metrics',
                },
                {
                    name: 'ProductClicks',
                    caption: 'Lượt click sản phẩm',
                    type: aggregations['ProductClicks'] || 'Sum',
                    showSubTotals: false,
                    category: 'metrics',
                },
            ];

            // Filter values based on permissions
            return allValues.filter((value) => {
                if (permissions.viewAll || permissions.viewAllAdvertisers)
                    return true;
                if (
                    value.category === 'spending' &&
                    (permissions.viewSpending || permissions.viewAllAdvertisers)
                )
                    return true;
                if (
                    value.category === 'metrics' &&
                    (permissions.viewMetrics || permissions.viewAllAdvertisers)
                )
                    return true;
                if (
                    value.category === 'restricted' &&
                    (permissions.viewAll || permissions.viewAllAdvertisers)
                )
                    return true;
                return false;
            });
        }

        // Get currency values with per-column aggregation settings
        async getCurrencyValuesWithAggregations() {
            // Get stored column aggregations from localStorage
            const currentAggregations = this.getStoredColumnAggregations();
            return await this.getCurrencyValues(currentAggregations);
        }

        // Get stored column aggregations from localStorage
        getStoredColumnAggregations() {
            const stored = localStorage.getItem('productColumnAggregations');
            if (stored) {
                try {
                    return JSON.parse(stored);
                } catch (e) {
                    console.warn(
                        'Failed to parse stored column aggregations, using defaults'
                    );
                }
            }

            // Return default aggregations for products
            return {
                Cost: 'Sum',
                GrossRevenue: 'Sum',
                Orders: 'Sum',
                ROAS: 'Avg',
                ACOS: 'Avg',
                Impressions: 'Sum',
                Clicks: 'Sum',
                CTR: 'Avg',
                CostPerOrder: 'Avg',
                ROI: 'Avg',
                AdsRevenue: 'Sum',
                OrganicRevenue: 'Sum',
                ProductPrice: 'Avg',
                QuantitySold: 'Sum',
                TACOS: 'Avg',
                ConversionRate: 'Avg',
            };
        }

        // Store column aggregations to localStorage
        storeColumnAggregations(aggregations) {
            localStorage.setItem(
                'productColumnAggregations',
                JSON.stringify(aggregations)
            );
        }

        // ✅ Helper methods
        createLookup(array, keyField) {
            if (!array) return {};
            const lookup = {};
            array.forEach((item) => {
                lookup[item[keyField]] = item;
            });
            return lookup;
        }

        // ✅ Date helper functions removed - using shared date-helper.js instead

        // ✅ Export and view methods
        exportToExcel(fileName = 'TikTok_GMV_Max_Product_Analysis') {
            if (this.pivotTableObj) {
                this.pivotTableObj.excelExport({
                    fileName: `${fileName}_${new Date()
                        .toISOString()
                        .slice(0, 10)}.xlsx`,
                    includeHeader: true,
                });
            }
        }

        // ✅ Debounced conditional formatting update
        debouncedUpdateConditionalFormatting() {
            if (this._formattingTimer) {
                clearTimeout(this._formattingTimer);
            }

            this._formattingTimer = setTimeout(() => {
                this.updateConditionalFormatting();
            }, this._formattingDebounceMs);
        }

        // Update conditional formatting (called by debounced method)
        updateConditionalFormatting() {
            if (!this.pivotTableObj) return;

            try {
                this.pivotTableObj.dataSourceSettings.conditionalFormatSettings =
                    this.generateProductHeatmapFormatting();
                this.pivotTableObj.refresh();
                console.log('✅ Conditional formatting updated');
            } catch (error) {
                console.error('Error updating conditional formatting:', error);
            }
        }

        // Update heatmap thresholds for current currency
        updateHeatmapThresholdsForCurrency() {
            this.debouncedUpdateConditionalFormatting();
        }

        exportToPdf(fileName = 'TikTok_GMV_Max_Product_Report') {
            if (this.pivotTableObj) {
                this.pivotTableObj.pdfExport({
                    fileName: `${fileName}_${new Date()
                        .toISOString()
                        .slice(0, 10)}.pdf`,
                    includeHeader: true,
                });
            }
        }

        showChart() {
            if (this.pivotTableObj) {
                this.pivotTableObj.displayOption.view = 'Chart';
                this.pivotTableObj.chartSettings.chartSeries.type = 'Column';
                this.pivotTableObj.refresh();
            }
        }

        showGrid() {
            if (this.pivotTableObj) {
                this.pivotTableObj.displayOption.view = 'Grid';
                this.pivotTableObj.refresh();
            }
        }

        async refreshData(newData) {
            if (this.pivotTableObj && newData) {
                // ✅ Don't overwrite original data when filtering - just refresh display
                this.pivotTableObj.dataSourceSettings.dataSource =
                    await this.extractPivotData(['product'], newData);
                this.pivotTableObj.refresh();
            }
        }

        handleCellClick(args) {
            if (args.currentCell && args.data) {
                const cellData = args.data[0];
                if (!cellData) return;

                // Show alerts for poor performing products
                if (
                    cellData.ProductHealth === 'Poor' ||
                    cellData.SalesStatus === 'Very Slow'
                ) {
                    this.showProductAlert(cellData);
                }
            }
        }

        showProductAlert(data) {
            const message = `
                <strong>⚠️ Cảnh báo hiệu suất sản phẩm!</strong><br>
                Sản phẩm: ${data.ProductName}<br>
                ROAS: ${data.ROAS}x (${data.ROASStatus})<br>
                ACOS: ${data.ACOS}% (${data.ACOSStatus})<br>
                Số lượng bán: ${data.QuantitySold} (${data.SalesStatus})<br>
                <em>Cần tối ưu hóa hoặc dừng quảng cáo!</em>
            `;
            showToast('warning', message);
        }

        updateProductInsights() {
            // Dashboard is now independent of pivot table, no need to regenerate
            // Only update pivot table specific insights if needed
        }

        getSmartTimeColumns() {
            try {
                if (
                    window.dateRangePicker &&
                    window.dateRangePicker.startDate &&
                    window.dateRangePicker.endDate
                ) {
                    const dateRange = {
                        from: new Date(window.dateRangePicker.startDate),
                        to: new Date(window.dateRangePicker.endDate),
                    };
                    return getSmartTimeGrouping(dateRange);
                }
                return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
            } catch (error) {
                console.error('Error in getSmartTimeColumns:', error);
                return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
            }
        }

        formatCurrency(amount, symbol = '$') {
            if (!amount) return `${symbol}0`;
            const isVnd = this.currentCurrency === 'VND';
            const locale = isVnd ? 'vi-VN' : 'en-US';
            return new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: isVnd ? 'VND' : 'USD',
                maximumFractionDigits: 0,
                minimumFractionDigits: 0,
            })
                .format(amount)
                .replace('₫', 'đ');
        }

        // ✅ Helper method to get current selected values
        getCurrentSelectedValues() {
            let savedValues = localStorage.getItem('productValueSelectedState');
            if (savedValues) {
                try {
                    return JSON.parse(savedValues);
                } catch (e) {
                    console.warn('Error parsing saved values:', e);
                    return [
                        'GrossRevenue',
                        'Cost',
                        'ROAS',
                        'TACOS',
                        'ProductImpressions',
                        'Orders',
                    ];
                }
            }
            return [
                'GrossRevenue',
                'Cost',
                'ROAS',
                'ACOS',
                'QuantitySold',
                'Orders',
            ];
        }

        // Update pivot table with column aggregations
        async updatePivotTableWithColumnAggregations() {
            if (!this.pivotTableObj) return;

            try {
                const currentAggregations = this.getStoredColumnAggregations();
                const selectedValues = JSON.parse(
                    localStorage.getItem('productValueSelectedState') || '[]'
                );

                if (selectedValues.length === 0) {
                    return;
                }

                // Create new value definitions with current column aggregations
                const allValues = await this.getCurrencyValues(
                    currentAggregations
                );
                const newValues = allValues.filter((v) =>
                    selectedValues.includes(v.name)
                );

                // Update pivot table values
                this.pivotTableObj.dataSourceSettings.values = newValues;

                // Refresh pivot table
                this.pivotTableObj.refresh();
            } catch (error) {
                console.error(
                    'Failed to update pivot table with aggregations:',
                    error
                );
            }
        }

        // Reset all column aggregations
        resetAllColumnAggregations() {
            const defaultAggregations = {
                Cost: 'Sum',
                GrossRevenue: 'Sum',
                Orders: 'Sum',
                ROAS: 'Avg',
                ACOS: 'Avg',
                Impressions: 'Sum',
                Clicks: 'Sum',
                CTR: 'Avg',
                CostPerOrder: 'Avg',
                ROI: 'Avg',
                AdsRevenue: 'Sum',
                OrganicRevenue: 'Sum',
                ProductPrice: 'Avg',
                QuantitySold: 'Sum',
                TACOS: 'Avg',
                ConversionRate: 'Avg',
            };

            this.storeColumnAggregations(defaultAggregations);
            this.updatePivotTableWithColumnAggregations();
        }
    }

    // ✅ Load data from API (lazy loading)
    async function loadDataFromAPI() {
        const loadingIndicator = document.getElementById(
            'data-loading-indicator'
        );
        if (loadingIndicator) {
            loadingIndicator.style.display = 'block';
        }

        try {
            // Get date range from dashboard config or use defaults
            const fromDate =
                window.dashboardConfig?.fromDate ||
                new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                    .toISOString()
                    .split('T')[0];
            const toDate =
                window.dashboardConfig?.toDate ||
                new Date().toISOString().split('T')[0];

            const response = await fetch(
                `/api/fact-gmv-max-product/data?fromDate=${fromDate}&toDate=${toDate}`
            );

            if (!response.ok) {
                throw new Error(
                    `API request failed: ${response.status} ${response.statusText}`
                );
            }

            const data = await response.json();
            window.initialGmvMaxProductData = data;

            // Refresh filter data after loading
            if (window.refreshFilterData) {
                window.refreshFilterData();
            }

            return data;
        } catch (error) {
            console.error('❌ Failed to load data from API:', error);
            if (window.showToast) {
                showToast('error', 'Lỗi khi tải dữ liệu từ server');
            }
            throw error;
        } finally {
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }
    }

    // ✅ Initialize product pivot table (dashboard handled separately)
    async function initializeProductPivotTable() {
        // Check if ej (Syncfusion) is loaded
        if (typeof ej === 'undefined') {
            console.warn('⚠️ Syncfusion (ej) not loaded yet, retrying...');
            setTimeout(initializeProductPivotTable, 500);
            return;
        }

        const productDashboard = new TiktokGmvMaxProductPivotTable();

        productDashboard
            .initial()
            .then(() => {
                window.tiktokGmvMaxProductPivotTable = productDashboard;

                console.log('✅ Pivot table initialized successfully');
            })
            .catch((error) => {
                console.error(
                    '❌ Error initializing product pivot table:',
                    error
                );
                console.error('❌ Error details:', error.stack);
                if (window.showToast) {
                    showToast(
                        'error',
                        'Lỗi khi khởi tạo bảng điều khiển sản phẩm'
                    );
                }
            });
    }

    // ✅ Wait for DOM and scripts to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize dashboard using dashboard module
            if (window.ProductDashboard) {
                window.ProductDashboard.initialize();
            }
            // Load initial data for pivot table
            loadDataFromAPI();
            // Then initialize pivot table
            initializeProductPivotTable();
        });
    } else {
        // Initialize dashboard using dashboard module
        if (window.ProductDashboard) {
            window.ProductDashboard.initialize();
        }
        // Load initial data for pivot table
        loadDataFromAPI();
        // Then initialize pivot table
        initializeProductPivotTable();
    }

    // ✅ Simple loadPivotTable like FactBalance - no filter preservation
    window.loadPivotTable = function loadPivotTable(fromDate, toDate) {
        // Clear cached dashboard data to force refresh
        window.dashboardData = null;
        $.ajax({
            url: '/api/fact-gmv-max-product/data',
            data: { fromDate: fromDate, toDate: toDate },
            success: async function (response) {
                if (window.tiktokGmvMaxProductPivotTable) {
                    try {
                        // Update data and refresh pivot table - simple approach
                        await window.tiktokGmvMaxProductPivotTable.refreshData(
                            response
                        );

                        showToast(
                            'success',
                            `Đã lọc sản phẩm từ ${fromDate} đến ${toDate}`
                        );
                    } catch (error) {
                        console.error('Error refreshing product data:', error);
                        showToast('error', 'Lỗi khi cập nhật dữ liệu sản phẩm');
                    }
                }
            },
            error: function (xhr) {
                showToast('error', 'Lỗi khi lọc dữ liệu sản phẩm');
            },
        });
    };

    // ✅ Global function to refresh currency
    window.refreshProductCurrency = function refreshProductCurrency() {
        if (window.tiktokGmvMaxProductPivotTable) {
            window.tiktokGmvMaxProductPivotTable.refreshCurrency();
        }
        // Also refresh dashboard with new currency
        if (window.ProductDashboard) {
            const currentCurrency =
                localStorage.getItem('selectedCurrency') || 'USD';
            window.ProductDashboard.refresh(currentCurrency);
        }
    };
});

// Value Selection Modal logic for products
document.addEventListener('DOMContentLoaded', function () {
    function getStoredColumnAggregations() {
        const stored = localStorage.getItem('productColumnAggregations');
        if (stored) {
            try {
                return JSON.parse(stored);
            } catch (e) {}
        }

        // Return default aggregations for products
        return {
            Cost: 'Sum',
            GrossRevenue: 'Sum',
            Orders: 'Sum',
            ROAS: 'Avg',
            ACOS: 'Avg',
            Impressions: 'Sum',
            Clicks: 'Sum',
            CTR: 'Avg',
            CostPerOrder: 'Avg',
            ROI: 'Avg',
            AdsRevenue: 'Sum',
            OrganicRevenue: 'Sum',
            ProductPrice: 'Avg',
            QuantitySold: 'Sum',
            TACOS: 'Avg',
            ConversionRate: 'Avg',
        };
    }

    // Function to get value definitions with column aggregations
    async function getValueDefsWithColumnAggregations(
        columnAggregations = null
    ) {
        const aggregations =
            columnAggregations || getStoredColumnAggregations();

        // Wait for ABP to be ready and get permissions
        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('product');

        // Debug logging
        console.log('🔍 Modal permissions debug:', permissions);
        console.log('🔍 Available permissions:', {
            viewSpending: permissions.viewSpending,
            viewMetrics: permissions.viewMetrics,
            viewAll: permissions.viewAll,
            viewAllAdvertisers: permissions.viewAllAdvertisers,
        });

        const allValueDefs = [
            {
                name: 'GrossRevenue',
                caption: `Tổng doanh thu ($)`,
                type: aggregations['GrossRevenue'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'AdsRevenue',
                caption: `Doanh thu quảng cáo ($)`,
                type: aggregations['AdsRevenue'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'OrganicRevenue',
                caption: `Doanh thu tự nhiên ($)`,
                type: aggregations['OrganicRevenue'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'Cost',
                caption: `Chi phí quảng cáo ($)`,
                type: aggregations['Cost'] || 'Sum',
                category: 'spending',
            },
            {
                name: 'CostPerOrder',
                caption: `Chi phí mỗi đơn ($)`,
                type: aggregations['CostPerOrder'] || 'Avg',
                category: 'spending',
            },
            {
                name: 'ProductPrice',
                caption: `Giá sản phẩm ($)`,
                type: aggregations['ProductPrice'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'ROAS',
                caption: 'ROAS (Return on Ad Spend)',
                type: aggregations['ROAS'] || 'Avg',
                category: 'metrics',
            },
            {
                name: 'TACOS',
                caption: 'TACOS (%)',
                type: aggregations['TACOS'] || 'Avg',
                category: 'restricted',
            },
            {
                name: 'Orders',
                caption: 'Số đơn hàng',
                type: aggregations['Orders'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'QuantitySold',
                caption: 'Số lượng bán được',
                type: aggregations['QuantitySold'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'ProductImpressions',
                caption: 'Lượt xem sản phẩm',
                type: aggregations['ProductImpressions'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'ProductClicks',
                caption: 'Lượt click sản phẩm',
                type: aggregations['ProductClicks'] || 'Sum',
                category: 'metrics',
            },
            {
                name: 'ConversionRate',
                caption: 'Tỷ lệ chuyển đổi (%)',
                type: aggregations['ConversionRate'] || 'Avg',
                category: 'metrics',
            },
        ];

        // Filter values based on permissions
        const filteredValues = allValueDefs.filter((value) => {
            if (permissions.viewAll || permissions.viewAllAdvertisers)
                return true;
            if (
                value.category === 'spending' &&
                (permissions.viewSpending || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'metrics' &&
                (permissions.viewMetrics || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'restricted' &&
                (permissions.viewAll || permissions.viewAllAdvertisers)
            )
                return true;
            return false;
        });

        // Debug logging
        console.log('🔍 Total value definitions:', allValueDefs.length);
        console.log('🔍 Filtered value definitions:', filteredValues.length);
        console.log(
            '🔍 Filtered values:',
            filteredValues.map((v) => v.name)
        );

        // No fallback: If no values are filtered by permissions, return empty array
        if (filteredValues.length === 0) {
            console.warn(
                '⚠️ No values filtered by permissions - user has no access to any fields'
            );
            return [];
        }

        return filteredValues;
    }

    // Get current column aggregations from localStorage
    let currentColumnAggregations = getStoredColumnAggregations();

    // Initialize valueDefs with current column aggregations (will be updated when modal opens)
    let valueDefs = [];
    const defaultValues = [
        'GrossRevenue',
        'Cost',
        'ROAS',
        'TACOS',
        'ProductImpressions',
        'Orders',
    ];
    let valueSelectedStore =
        localStorage.getItem('productValueSelectedState') ?? null;
    valueSelectedStore = JSON.parse(valueSelectedStore) ?? defaultValues;
    let valueSelectedState = valueSelectedStore;

    // Function to render value checkboxes with individual aggregation dropdowns
    async function renderValueCheckboxesWithAggregations(selected) {
        const container = document.getElementById('value-checkbox-list');
        container.innerHTML = '';

        // Get filtered value definitions based on permissions
        const filteredValueDefs = await getValueDefsWithColumnAggregations(
            currentColumnAggregations
        );

        // Debug logging
        console.log(
            '🔍 Rendering value checkboxes:',
            filteredValueDefs.length,
            'fields'
        );
        console.log('🔍 Selected values:', selected);

        filteredValueDefs.forEach((v) => {
            const col = document.createElement('div');
            col.className = 'col-6 col-md-3 mb-3';

            const currentAggregation =
                currentColumnAggregations[v.name] || 'Avg'; // Get from stored aggregations

            col.innerHTML = `
                <div class='card border-light'>
                    <div class='card-body p-2'>
                        <div class='form-check d-flex align-items-center mb-2'>
                            <input class='form-check-input value-checkbox me-2' type='checkbox' value='${
                                v.name
                            }' id='cb-${v.name}' ${
                selected.includes(v.name) ? 'checked' : ''
            }>
                            <label class='form-check-label fw-bold' for='cb-${
                                v.name
                            }'>${v.caption}</label>
                        </div>
                        <div class='d-flex align-items-center'>
                            <label class='form-label small mb-0 me-2'>Chế độ:</label>
                            <select class='form-select form-select-sm column-aggregation' data-column='${
                                v.name
                            }' style='width: auto;'>
                                <option value='Sum' ${
                                    currentAggregation === 'Sum'
                                        ? 'selected'
                                        : ''
                                }>Tổng (Sum)</option>
                                <option value='Avg' ${
                                    currentAggregation === 'Avg'
                                        ? 'selected'
                                        : ''
                                }>Trung bình (Avg)</option>
                                <option value='Max' ${
                                    currentAggregation === 'Max'
                                        ? 'selected'
                                        : ''
                                }>Lớn nhất (Max)</option>
                                <option value='Min' ${
                                    currentAggregation === 'Min'
                                        ? 'selected'
                                        : ''
                                }>Nhỏ nhất (Min)</option>
                                <option value='First' ${
                                    currentAggregation === 'First'
                                        ? 'selected'
                                        : ''
                                }>Bản ghi đầu (First)</option>
                                <option value='Last' ${
                                    currentAggregation === 'Last'
                                        ? 'selected'
                                        : ''
                                }>Bản ghi cuối (Last)</option>
                                <option value='Count' ${
                                    currentAggregation === 'Count'
                                        ? 'selected'
                                        : ''
                                }>Đếm (Count)</option>
                                <option value='DistinctCount' ${
                                    currentAggregation === 'DistinctCount'
                                        ? 'selected'
                                        : ''
                                }>Đếm duy nhất (Distinct)</option>
                            </select>
                        </div>
                    </div>
                </div>`;
            container.appendChild(col);
        });

        // Add event listeners for aggregation dropdowns
        document.querySelectorAll('.column-aggregation').forEach((select) => {
            select.addEventListener('change', function () {
                const columnName = this.dataset.column;
                const newAggregation = this.value;

                // Update local state
                if (!window.productColumnAggregations) {
                    window.productColumnAggregations = {};
                }
                window.productColumnAggregations[columnName] = newAggregation;

                // Store to localStorage
                localStorage.setItem(
                    'productColumnAggregations',
                    JSON.stringify(window.productColumnAggregations)
                );
            });
        });

        // Add event listeners for checkboxes to update selected count
        document.querySelectorAll('.value-checkbox').forEach((checkbox) => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        // Update selected count initially
        updateSelectedCount();
    }

    // Function to update selected count display
    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll(
            '.value-checkbox:checked'
        ).length;
        const countElement = document.getElementById('selected-count');
        if (countElement) {
            countElement.textContent = selectedCount;
            countElement.className =
                selectedCount > 0
                    ? 'fw-bold text-success'
                    : 'fw-bold text-muted';
        }
    }

    $('#open-value-selection-modal').on('click', async function () {
        // Sync column aggregations from localStorage
        currentColumnAggregations = getStoredColumnAggregations();

        // Update valueDefs with current aggregations and permissions
        valueDefs = await getValueDefsWithColumnAggregations(
            currentColumnAggregations
        );

        // Render checkboxes with current selected values
        await renderValueCheckboxesWithAggregations(valueSelectedState);

        // Show modal
        new bootstrap.Modal(
            document.getElementById('valueSelectionModal')
        ).show();
    });

    $('#select-default').on('click', async function () {
        // Only select default values that are available based on permissions
        valueSelectedState = defaultValues.filter((field) =>
            valueDefs.some((v) => v.name === field)
        );
        await renderValueCheckboxesWithAggregations(valueSelectedState);
    });

    $('#select-all-revenue').on('click', async function () {
        // Only select revenue fields that are available based on permissions
        const revenueFields = ['GrossRevenue', 'AdsRevenue', 'OrganicRevenue'];
        valueSelectedState = revenueFields.filter((field) =>
            valueDefs.some((v) => v.name === field)
        );
        await renderValueCheckboxesWithAggregations(valueSelectedState);
    });

    $('#select-all-metrics').on('click', async function () {
        // Only select metrics fields that are available based on permissions
        const metricsFields = ['ROAS', 'TACOS', 'ConversionRate'];
        valueSelectedState = metricsFields.filter((field) =>
            valueDefs.some((v) => v.name === field)
        );
        await renderValueCheckboxesWithAggregations(valueSelectedState);
    });

    $('#select-all-product').on('click', async function () {
        // Only select product fields that are available based on permissions
        const productFields = [
            'ProductPrice',
            'QuantitySold',
            'Orders',
            'CostPerOrder',
        ];
        valueSelectedState = productFields.filter((field) =>
            valueDefs.some((v) => v.name === field)
        );
        await renderValueCheckboxesWithAggregations(valueSelectedState);
    });

    $('#select-all').on('click', async function () {
        valueSelectedState = valueDefs.map((v) => v.name);
        await renderValueCheckboxesWithAggregations(valueSelectedState);
    });

    $('#apply-value-selection').on('click', function () {
        // Get all checked values
        const selected = Array.from(
            document.querySelectorAll('.value-checkbox:checked')
        ).map((cb) => cb.value);
        valueSelectedState = selected;
        localStorage.setItem(
            'productValueSelectedState',
            JSON.stringify(valueSelectedState)
        );

        if (window.tiktokGmvMaxProductPivotTable && selected.length > 0) {
            // Update pivot table with current column aggregations
            window.tiktokGmvMaxProductPivotTable
                .updatePivotTableWithColumnAggregations()
                .catch((error) => {
                    console.error('Error updating pivot table:', error);
                });
        }

        // Close modal
        bootstrap.Modal.getInstance(
            document.getElementById('valueSelectionModal')
        ).hide();

        // Refresh pivot table
        setTimeout(() => {
            if (window.tiktokGmvMaxProductPivotTable) {
                window.tiktokGmvMaxProductPivotTable.showGrid();
            }
        }, 100);
    });
});
