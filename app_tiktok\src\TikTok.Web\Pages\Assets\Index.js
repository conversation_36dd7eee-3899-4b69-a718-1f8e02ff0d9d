$(function () {
    var l = abp.localization.getResource('TikTok');

    // Search parameters
    var searchParams = {
        assetId: '',
        assetName: ''
    };

    var dataTable = $('#AssetsTable').DataTable(
        abp.libs.datatables.normalizeConfiguration({
            serverSide: true,
            paging: true,
            order: [[1, 'asc']],
            searching: false, // Disable default search since we use custom search
            scrollX: true,
            ajax: abp.libs.datatables.createAjax(tikTok.assets.asset.getList, function () {
                return searchParams;
            }),
            columnDefs: [
                {
                    title: l('Asset:AssetId'),
                    data: 'assetId',
                },
                {
                    title: l('Asset:AssetName'),
                    data: 'assetName',
                },
                {
                    title: l('Asset:AssetType'),
                    data: 'assetType',
                    render: function (data) {
                        return l('Asset:AssetType:' + data);
                    },
                },
                {
                    title: l('BcId'),
                    data: 'bcId',
                },
                {
                    title: l('Asset:OwnerBcName'),
                    data: 'ownerBcName',
                },
                {
                    title: l('Actions'),
                    rowAction: {
                        items: [
                            {
                                text: l('Edit'),
                                visible:
                                    abp.auth.isGranted('TikTok.Assets.Edit'),
                                action: function (data) {
                                    editModal.open({ id: data.record.id });
                                },
                            },
                            {
                                text: l('Delete'),
                                visible: abp.auth.isGranted(
                                    'TikTok.Assets.Delete'
                                ),
                                confirmMessage: function (data) {
                                    return l(
                                        'AssetDeletionConfirmationMessage',
                                        data.record.assetName
                                    );
                                },
                                action: function (data) {
                                    tikTok.assets.asset
                                        .delete(data.record.id)
                                        .then(function () {
                                            abp.notify.info(
                                                l('SuccessfullyDeleted')
                                            );
                                            dataTable.ajax.reload();
                                        });
                                },
                            },
                        ],
                    },
                },
            ],
        })
    );

    var createModal = new abp.ModalManager(abp.appPath + 'Assets/CreateModal');
    var editModal = new abp.ModalManager(abp.appPath + 'Assets/EditModal');

    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    $('#NewAssetButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });

    // Search functionality
    function saveSearchParams() {
        searchParams.assetId = $('#AssetIdFilter').val() || '';
        searchParams.assetName = $('#AssetNameFilter').val() || '';
        updateSearchTags();
    }

    function clearSearchParams() {
        searchParams.assetId = '';
        searchParams.assetName = '';
        $('#AssetIdFilter').val('');
        $('#AssetNameFilter').val('');
        updateSearchTags();
    }

    function updateSearchTags() {
        var tagsContainer = $('#SearchTags');
        var tagsPanel = $('#SearchTagsPanel');
        tagsContainer.empty();

        var hasFilters = false;

        if (searchParams.assetId) {
            hasFilters = true;
            tagsContainer.append(
                '<span class="badge bg-primary me-2">' +
                    l('Asset:AssetId') + ': ' + searchParams.assetId +
                    ' <button type="button" class="btn-close btn-close-white ms-1" data-field="assetId"></button>' +
                '</span>'
            );
        }

        if (searchParams.assetName) {
            hasFilters = true;
            tagsContainer.append(
                '<span class="badge bg-primary me-2">' +
                    l('Asset:AssetName') + ': ' + searchParams.assetName +
                    ' <button type="button" class="btn-close btn-close-white ms-1" data-field="assetName"></button>' +
                '</span>'
            );
        }

        if (hasFilters) {
            tagsPanel.show();
        } else {
            tagsPanel.hide();
        }
    }

    // Event handlers
    $('#SearchForm').on('submit', function (e) {
        e.preventDefault();
        saveSearchParams();
        dataTable.ajax.reload();
    });

    $('#ClearSearchBtn').on('click', function (e) {
        e.preventDefault();
        clearSearchParams();
        dataTable.ajax.reload();
    });

    // Enter key on search fields
    $('#AssetIdFilter, #AssetNameFilter').on('keypress', function (e) {
        if (e.which === 13) {
            e.preventDefault();
            saveSearchParams();
            dataTable.ajax.reload();
        }
    });

    // Remove individual search tags
    $(document).on('click', '.btn-close[data-field]', function (e) {
        e.preventDefault();
        var field = $(this).data('field');

        if (field === 'assetId') {
            searchParams.assetId = '';
            $('#AssetIdFilter').val('');
        } else if (field === 'assetName') {
            searchParams.assetName = '';
            $('#AssetNameFilter').val('');
        }

        updateSearchTags();
        dataTable.ajax.reload();
    });

    // Auto-search on input change (with debounce)
    var searchTimeout;
    function debouncedSearch() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function () {
            saveSearchParams();
            dataTable.ajax.reload();
        }, 500);
    }

    // Bind auto-search to filter inputs
    $('#AssetIdFilter, #AssetNameFilter').on('input', debouncedSearch);
});
