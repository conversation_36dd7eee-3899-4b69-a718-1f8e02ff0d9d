using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.RawGmvMaxProductCreativeReports;

namespace TikTok.Repositories
{
    /// <summary>
    /// DapperRepository interface cho RawGmvMaxProductCreativeReportEntity
    /// Sử dụng Dapper để thực hiện các truy vấn phức tạp với phân trang và lọc
    /// Repository chỉ thao tác với Entity, không sử dụng DTO
    /// </summary>
    public interface IRawGmvMaxProductCreativeReportDapperRepository
    {
        /// <summary>
        /// L<PERSON>y danh sách RawGmvMaxProductCreativeReportEntity với phân trang và lọc
        /// </summary>
        /// <param name="searchText">Tìm kiếm theo text (Title, CampaignId, ItemGroupId, ItemId)</param>
        /// <param name="fromDate"><PERSON><PERSON><PERSON> t<PERSON> ngày</param>
        /// <param name="toDate"><PERSON><PERSON><PERSON> đến ngày</param>
        /// <param name="campaignId">Lọc theo Campaign ID</param>
        /// <param name="shopContentTypes">Lọc theo loại nội dung shop</param>
        /// <param name="creativeDeliveryStatuses">Lọc theo trạng thái giao hàng creative</param>
        /// <param name="skipCount">Số bản ghi bỏ qua</param>
        /// <param name="maxResultCount">Số bản ghi tối đa</param>
        /// <param name="sorting">Sắp xếp</param>
        /// <returns>Danh sách entity</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetListAsync(
            string? searchText = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? campaignId = null,
            List<string>? campaignIds = null,
            List<ShopContentType>? shopContentTypes = null,
            List<CreativeDeliveryStatus>? creativeDeliveryStatuses = null,
            int skipCount = 0,
            int maxResultCount = 20,
            string? sorting = null);

        /// <summary>
        /// Lấy thông tin chi tiết RawGmvMaxProductCreativeReportEntity theo ID
        /// </summary>
        /// <param name="id">ID của entity</param>
        /// <returns>Entity hoặc null nếu không tìm thấy</returns>
        Task<RawGmvMaxProductCreativeReportEntity?> GetAsync(Guid id);

        /// <summary>
        /// Lấy danh sách Campaign IDs để sử dụng trong dropdown filter
        /// </summary>
        /// <returns>Danh sách Campaign IDs</returns>
        Task<List<string>> GetCampaignIdsAsync();

        /// <summary>
        /// Đếm tổng số bản ghi theo điều kiện lọc (không phân trang)
        /// </summary>
        /// <param name="searchText">Tìm kiếm theo text</param>
        /// <param name="fromDate">Lọc từ ngày</param>
        /// <param name="toDate">Lọc đến ngày</param>
        /// <param name="campaignId">Lọc theo Campaign ID</param>
        /// <param name="shopContentTypes">Lọc theo loại nội dung shop</param>
        /// <param name="creativeDeliveryStatuses">Lọc theo trạng thái giao hàng creative</param>
        /// <returns>Tổng số bản ghi</returns>
        Task<long> GetCountAsync(
            string? searchText = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? campaignId = null,
            List<string>? campaignIds = null,
            List<ShopContentType>? shopContentTypes = null,
            List<CreativeDeliveryStatus>? creativeDeliveryStatuses = null);

        /// <summary>
        /// Lấy thống kê video theo trạng thái (group by CreativeDeliveryStatus)
        /// </summary>
        /// <param name="searchText">Tìm kiếm theo text</param>
        /// <param name="fromDate">Lọc từ ngày</param>
        /// <param name="toDate">Lọc đến ngày</param>
        /// <param name="campaignIds">Lọc theo Campaign IDs</param>
        /// <param name="shopContentTypes">Lọc theo loại nội dung shop</param>
        /// <returns>Danh sách thống kê theo trạng thái</returns>
        Task<List<VideoStatusCountDto>> GetVideoStatusStatisticsAsync(
            string? searchText = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            List<string>? campaignIds = null,
            List<ShopContentType>? shopContentTypes = null);
    }
}
