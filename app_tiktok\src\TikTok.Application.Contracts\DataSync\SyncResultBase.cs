﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Consts;

namespace TikTok.DataSync
{
    /// <summary>
    /// Metrics API cho service
    /// </summary>
    public class ApiMetrics
    {
        /// <summary>
        /// Tên service
        /// </summary>
        public string ServiceName { get; set; } = string.Empty;

        /// <summary>
        /// Rate gọi API theo giây (số lượt gọi/giây)
        /// </summary>
        public double ApiCallsPerSecond { get; set; }

        /// <summary>
        /// Tổng số lượt gọi API cho service trong ngày
        /// </summary>
        public int TotalApiCallsToday { get; set; }

        /// <summary>
        /// Thời gian bắt đầu đo lường
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc đo lường
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Tổng thời gian thực hiện (giây)
        /// </summary>
        public double TotalDurationSeconds => (EndTime - StartTime).TotalSeconds;
    }

    public class SyncResultBase
    {
        public string Code { get; set; } = TikTokApiCodes.Success.ToString();

        /// <summary>
        /// Thông báo lỗi (nếu có)
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Thành công hay không
        /// </summary>
        public bool IsSuccess => string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// Tổng số bản ghi đã đồng bộ
        /// </summary>
        public virtual int TotalSynced { get; set; }

        /// <summary>
        /// Số bản ghi mới được thêm
        /// </summary>
        public int NewRecords { get; set; }

        /// <summary>
        /// Số bản ghi được cập nhật
        /// </summary>
        public int UpdatedRecords { get; set; }

        /// <summary>
        /// Số bản ghi lỗi
        /// </summary>
        public int ErrorRecords { get; set; }

        /// <summary>
        /// Metrics API cho service
        /// </summary>
        public ApiMetrics? ApiMetrics { get; set; }
    }
}
