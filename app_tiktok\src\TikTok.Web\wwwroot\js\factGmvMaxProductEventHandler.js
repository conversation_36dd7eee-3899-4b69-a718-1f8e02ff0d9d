$(function () {
    // Event handlers for GMV Max Product Dashboard - Simple pattern like FactBalance

    $('#refresh-data').click(function () {
        showLoading();
        setTimeout(() => {
            // ✅ Explicitly update dashboard when refresh button is clicked
            if (window.tiktokGmvMaxProductPivotTable) {
                window.tiktokGmvMaxProductPivotTable.generateDashboardSummary();
                window.tiktokGmvMaxProductPivotTable.refreshData(
                    window.initialGmvMaxProductData
                );
            }
            // ✅ NEW: Refresh product dashboard data
            if (window.refreshProductDashboard) {
                window.refreshProductDashboard();
            }
            window.updateSummaryCards();
            window.updateAlerts();
            hideLoading();
            showToast(
                'success',
                'Dữ liệu sản phẩm đã được làm mới thành công!'
            );
        }, 1000);
    });

    $('#export-excel').click(function () {
        if (window.tiktokGmvMaxProductPivotTable) {
            window.tiktokGmvMaxProductPivotTable.exportToExcel();
            showToast('success', 'Đã xuất dữ liệu sản phẩm sang Excel!');
        }
    });

    $('#export-pdf').click(function () {
        if (window.tiktokGmvMaxProductPivotTable) {
            window.tiktokGmvMaxProductPivotTable.exportToPdf();
            showToast('success', 'Đã xuất dữ liệu sản phẩm sang PDF!');
        }
    });

    // (removed) reset-aggregation-mode button

    $('#toggle-chart').click(function () {
        if (window.tiktokGmvMaxProductPivotTable) {
            const currentView =
                window.tiktokGmvMaxProductPivotTable.pivotTableObj.displayOption
                    .view;
            if (currentView === 'Grid' || currentView === 'Table') {
                window.tiktokGmvMaxProductPivotTable.showChart();
                $(this).html('<i class="fas fa-table"></i> Chuyển sang bảng');
            } else {
                window.tiktokGmvMaxProductPivotTable.showGrid();
                $(this).html(
                    '<i class="fas fa-chart-bar"></i> Chuyển sang biểu đồ'
                );
            }
        }
    });

    // ✅ Removed: Simple category filter - removed for better performance

    // ✅ Removed: Simple performance filter - removed for better performance

    // ✅ Simple sales volume filter - unique to products
    $('input[name="sales-filter"]').change(function () {
        if (this.checked && window.tiktokGmvMaxProductPivotTable) {
            const filterValue = this.id.replace('sales-', '');

            if (filterValue !== 'all') {
                // Apply filter to pivot table data - simple approach
                let filteredData = window.initialGmvMaxProductData;
                if (filterValue === 'high') {
                    filteredData = {
                        ...window.initialGmvMaxProductData,
                        factGmvMaxProducts:
                            window.initialGmvMaxProductData.factGmvMaxProducts.filter(
                                (f) => (f.quantitySold || 0) > 50
                            ),
                    };
                } else if (filterValue === 'medium') {
                    filteredData = {
                        ...window.initialGmvMaxProductData,
                        factGmvMaxProducts:
                            window.initialGmvMaxProductData.factGmvMaxProducts.filter(
                                (f) => {
                                    const qty = f.quantitySold || 0;
                                    return qty >= 10 && qty <= 50;
                                }
                            ),
                    };
                } else if (filterValue === 'low') {
                    filteredData = {
                        ...window.initialGmvMaxProductData,
                        factGmvMaxProducts:
                            window.initialGmvMaxProductData.factGmvMaxProducts.filter(
                                (f) => (f.quantitySold || 0) < 10
                            ),
                    };
                }

                window.tiktokGmvMaxProductPivotTable.refreshData(filteredData);
                showToast(
                    'success',
                    `Đã lọc theo ${this.nextElementSibling.textContent}`
                );
            } else {
                window.tiktokGmvMaxProductPivotTable.refreshData(
                    window.initialGmvMaxProductData
                );
                showToast('success', 'Đã hiển thị tất cả sản phẩm');
            }
        }
    });

    function parseDateToISO(dateStr) {
        if (window.sharedDateHelper && window.sharedDateHelper.parseDateToISO) {
            return window.sharedDateHelper.parseDateToISO(dateStr);
        }
        if (window.commonUtils && window.commonUtils.parseDateToISO) {
            return window.commonUtils.parseDateToISO(dateStr);
        }
        return null;
    }

    // ✅ Simple date filter - like FactBalance (just call API, no filter preservation)
    $('#date-filter-btn').click(function () {
        let fromDate = $('#from-date-filter').val();
        let toDate = $('#to-date-filter').val();
        if (!fromDate || !toDate) {
            showToast('error', 'Vui lòng chọn cả ngày từ và ngày đến');
            return;
        }

        // Normalize to ISO string for API
        fromDate = parseDateToISO(fromDate);
        toDate = parseDateToISO(toDate);

        if (!fromDate || !toDate) {
            showToast('error', 'Định dạng ngày không hợp lệ');
            return;
        }

        // Simple API call like FactBalance - no filter preservation
        if (window.loadPivotTable) window.loadPivotTable(fromDate, toDate);
    });

    // ✅ Quick date range buttons (use sharedDateHelper)
    function applyQuickRange(period) {
        const h = window.sharedDateHelper;
        const range =
            h && h.getQuickDateRange ? h.getQuickDateRange(period) : null;
        if (!range) return;
        const fromInput = document.getElementById('from-date-filter');
        const toInput = document.getElementById('to-date-filter');
        if (fromInput)
            fromInput.value =
                range.startDateISO ||
                (range.startDate &&
                    range.startDate.toISOString().split('T')[0]);
        if (toInput)
            toInput.value =
                range.endDateISO ||
                (range.endDate && range.endDate.toISOString().split('T')[0]);
    }

    $('#filter-today').click(function () {
        applyQuickRange('today');
    });
    $('#filter-week').click(function () {
        applyQuickRange('week');
    });
    $('#filter-month').click(function () {
        applyQuickRange('month');
    });
    $('#filter-quarter').click(function () {
        applyQuickRange('quarter');
    });

    // ✅ Help modal toggle
    $('#help-toggle').click(function () {
        const helpModal = new bootstrap.Modal(
            document.getElementById('helpModal')
        );
        helpModal.show();
    });

    // ✅ Auto-refresh toggle (placeholder for future implementation)
    $('#auto-refresh-toggle').click(function () {
        const status = $('#auto-refresh-status');
        if (status.text() === 'OFF') {
            status.text('ON');
            $(this)
                .removeClass('btn-outline-secondary')
                .addClass('btn-outline-success');
            showToast('info', 'Auto-refresh được bật (mỗi 5 phút)');
            // TODO: Implement actual auto-refresh functionality
        } else {
            status.text('OFF');
            $(this)
                .removeClass('btn-outline-success')
                .addClass('btn-outline-secondary');
            showToast('info', 'Auto-refresh được tắt');
        }
    });

    // ✅ NEW: Currency change handler for product dashboard
    $(document).on('currencyChanged', function (event, newCurrency) {
        // Refresh product dashboard with new currency
        if (window.refreshProductDashboard) {
            // Reset dashboard data loaded flag to force reload
            if (window.productDashboardDataLoaded !== undefined) {
                window.productDashboardDataLoaded = false;
            }
            window.refreshProductDashboard();
        }

        showToast(
            'success',
            `Đã chuyển đổi tiền tệ sang ${newCurrency} cho dashboard sản phẩm`
        );
    });
});

// ================= Advanced Filters (Syncfusion) - Apply-only refresh =================
document.addEventListener('DOMContentLoaded', function () {
    // Guard if Syncfusion not loaded
    if (typeof ej === 'undefined') return;

    // ✅ OPTIMIZED: Simplified filter state - chỉ cần các filter không phải date
    const filterState = {
        businessCenters: [],
        shops: [],
        keyword: '',
    };

    // ✅ SMART FILTER: Track date range changes for intelligent API calls
    let lastAppliedDateRange = null;

    // Extract choices from current data
    function getInitialData() {
        return (
            window.initialGmvMaxProductData || {
                factGmvMaxProducts: [],
                dimBusinessCenters: [],
                dimStores: [],
            }
        );
    }

    function uniqueBy(arr, key) {
        const set = new Set();
        const out = [];
        (arr || []).forEach((item) => {
            const val = item && item[key];
            if (val !== undefined && val !== null && !set.has(val)) {
                set.add(val);
                out.push(item);
            }
        });
        return out;
    }

    // Initialize Syncfusion controls
    let dateRangePicker, quickDateDropdown, bcMulti, shopMulti;

    // Initialize DateRangePicker
    dateRangePicker = new ej.calendars.DateRangePicker({
        placeholder: 'Chọn khoảng thời gian',
        locale: 'vi-VN',
        change: function (args) {
            // ✅ OPTIMIZED: Không cần lưu vào filterState, chỉ cần DateRangePicker
            // Date range changed, user needs to click Apply
        },
    });
    dateRangePicker.appendTo('#date-range-picker');

    // ✅ Store DateRangePicker globally for access from pivot table classes
    window.dateRangePicker = dateRangePicker;

    // Initialize Quick date dropdown
    const quickDateOptions = [
        { text: 'Hôm nay', value: 'today' },
        { text: '7 ngày', value: '7d' },
        { text: '30 ngày', value: '30d' },
        { text: 'Quý này', value: '90d' },
    ];
    quickDateDropdown = new ej.dropdowns.DropDownList({
        dataSource: quickDateOptions,
        fields: { text: 'text', value: 'value' },
        placeholder: 'Lọc nhanh',
        change: function (args) {
            try {
                // ✅ OPTIMIZED: Cập nhật DateRangePicker thay vì reset
                if (args.value) {
                    setQuickDateFilter(args.value);
                }
            } catch (error) {
                console.error(
                    '❌ Error in QuickDateDropdown change handler:',
                    error
                );
            }
        },
    });
    quickDateDropdown.appendTo('#quick-date-dropdown');

    // Initialize Business Center multiselect (empty initially)
    bcMulti = new ej.dropdowns.MultiSelect({
        dataSource: [],
        fields: { text: 'text', value: 'value' },
        mode: 'CheckBox',
        showSelectAll: true,
        selectAllText: 'Chọn tất cả',
        unSelectAllText: 'Bỏ chọn tất cả',
        showClearButton: true,
        enableFiltering: true,
        filterBarPlaceholder: 'Tìm kiếm trung tâm...',
        locale: 'vi-VN',
        placeholder: 'Chọn trung tâm',
        change: function (args) {
            filterState.businessCenters = args.value || [];
        },
    });
    bcMulti.appendTo('#business-center-multiselect');

    // Initialize Shop multiselect (empty initially)
    shopMulti = new ej.dropdowns.MultiSelect({
        dataSource: [],
        fields: { text: 'text', value: 'value' },
        mode: 'CheckBox',
        showSelectAll: true,
        selectAllText: 'Chọn tất cả',
        unSelectAllText: 'Bỏ chọn tất cả',
        showClearButton: true,
        enableFiltering: true,
        filterBarPlaceholder: 'Tìm kiếm shop...',
        locale: 'vi-VN',
        placeholder: 'Chọn shop',
        change: function (args) {
            filterState.shops = args.value || [];
        },
    });
    shopMulti.appendTo('#shop-multiselect');

    // Function to refresh filter data after API data is loaded
    function refreshFilterData() {
        const data = getInitialData();

        // Add small delay to ensure DOM is ready
        setTimeout(() => {
            refreshMultiselectData(data);
        }, 100);
    }

    function refreshMultiselectData(data) {
        // Refresh Business Center multiselect
        if (data.dimBusinessCenters && data.dimBusinessCenters.length > 0) {
            const bcData = data.dimBusinessCenters.map((b) => ({
                value: b.id,
                text: b.bcName || b.businessCenterName || b.name || b.id,
            }));
            const uniqueBcData = uniqueBy(bcData, 'value');

            // Try different approaches to update multiselect
            try {
                bcMulti.dataSource = uniqueBcData;
                bcMulti.refresh();
            } catch (error) {
                console.error('❌ Error refreshing BC multiselect:', error);
                // Try alternative approach
                try {
                    bcMulti.destroy();
                    bcMulti = new ej.dropdowns.MultiSelect({
                        dataSource: uniqueBcData,
                        fields: { text: 'text', value: 'value' },
                        mode: 'CheckBox',
                        showSelectAll: true,
                        selectAllText: 'Chọn tất cả',
                        unSelectAllText: 'Bỏ chọn tất cả',
                        showClearButton: true,
                        enableFiltering: true,
                        filterBarPlaceholder: 'Tìm kiếm trung tâm...',
                        locale: 'vi-VN',
                        placeholder: 'Chọn trung tâm',
                        change: function (args) {
                            filterState.businessCenters = args.value || [];
                        },
                    });
                    bcMulti.appendTo('#business-center-multiselect');
                } catch (recreateError) {
                    console.error(
                        '❌ Error recreating BC multiselect:',
                        recreateError
                    );
                }
            }
        }

        // Refresh Shop multiselect
        if (data.dimStores && data.dimStores.length > 0) {
            const shopData = data.dimStores.map((s) => ({
                value: s.id,
                text: s.storeName || s.name || s.id,
            }));
            const uniqueShopData = uniqueBy(shopData, 'value');

            // Try different approaches to update multiselect
            try {
                shopMulti.dataSource = uniqueShopData;
                shopMulti.refresh();
            } catch (error) {
                console.error('❌ Error refreshing Shop multiselect:', error);
                // Try alternative approach
                try {
                    shopMulti.destroy();
                    shopMulti = new ej.dropdowns.MultiSelect({
                        dataSource: uniqueShopData,
                        fields: { text: 'text', value: 'value' },
                        mode: 'CheckBox',
                        showSelectAll: true,
                        selectAllText: 'Chọn tất cả',
                        unSelectAllText: 'Bỏ chọn tất cả',
                        showClearButton: true,
                        enableFiltering: true,
                        filterBarPlaceholder: 'Tìm kiếm shop...',
                        locale: 'vi-VN',
                        placeholder: 'Chọn shop',
                        change: function (args) {
                            filterState.shops = args.value || [];
                        },
                    });
                    shopMulti.appendTo('#shop-multiselect');
                } catch (recreateError) {
                    console.error(
                        '❌ Error recreating Shop multiselect:',
                        recreateError
                    );
                }
            }
        }
    }

    // Make refreshFilterData available globally
    window.refreshFilterData = refreshFilterData;

    // ✅ Removed: Creative type, Product status, Category, and Performance multiselects

    // Keyword input
    const keywordInput = document.getElementById('keyword-search');
    if (keywordInput) {
        keywordInput.addEventListener('input', function (e) {
            filterState.keyword = e.target.value || '';
        });
    }

    // ✅ OPTIMIZED: Removed unnecessary helper functions - chỉ dùng DateRangePicker

    // Apply filters only when clicking the button
    const applyBtn = document.getElementById('apply-all-filters');
    if (applyBtn) {
        applyBtn.addEventListener('click', async function () {
            if (!validateDateRangeSelection()) {
                showToast(
                    'warning',
                    'Vui lòng chọn khoảng thời gian trước khi áp dụng!'
                );
                return;
            }

            // ✅ SMART LOGIC: Check if date range has changed
            const currentDateRange = getCurrentDateRangeFromPicker();
            const dateRangeChanged = hasDateRangeChanged(
                currentDateRange,
                lastAppliedDateRange
            );

            if (dateRangeChanged) {
                // ✅ Date range changed → Call API to fetch new data
                console.log('📅 Date range changed, calling API...');
                await callAPIWithNewDateRange(currentDateRange);
                lastAppliedDateRange = currentDateRange;
            } else {
                // ✅ Date range unchanged → Filter local data only
                console.log('📅 Date range unchanged, filtering local data...');
                filterLocalDataOnly();
            }
        });
    }

    // ✅ Helper: Get current date range from DateRangePicker
    function getCurrentDateRangeFromPicker() {
        if (
            dateRangePicker &&
            dateRangePicker.startDate &&
            dateRangePicker.endDate
        ) {
            return {
                from: new Date(dateRangePicker.startDate),
                to: new Date(dateRangePicker.endDate),
            };
        }
        return null;
    }

    // ✅ Helper: Check if date range has changed
    function hasDateRangeChanged(current, last) {
        if (!current && !last) return false;
        if (!current || !last) return true;

        return (
            current.from.getTime() !== last.from.getTime() ||
            current.to.getTime() !== last.to.getTime()
        );
    }

    // ✅ Helper: Call API with new date range
    async function callAPIWithNewDateRange(dateRange) {
        try {
            showLoading('Đang tải dữ liệu mới...');

            // Build API parameters
            const params = new URLSearchParams({
                fromDate: dateRange.from.toISOString().split('T')[0],
                toDate: dateRange.to.toISOString().split('T')[0],
            });

            // Call API
            const response = await fetch(
                `/api/fact-gmv-max-product/data?${params}`
            );
            if (!response.ok) {
                throw new Error(`API call failed: ${response.status}`);
            }

            const newData = await response.json();

            // Update global data
            window.initialGmvMaxProductData = newData;

            // Apply all filters to new data
            filterLocalDataOnly();

            hideLoading();
            showToast('success', 'Đã tải dữ liệu mới và áp dụng bộ lọc');
        } catch (error) {
            console.error('❌ Error calling API:', error);
            hideLoading();
            showToast('error', 'Lỗi khi tải dữ liệu mới: ' + error.message);
        }
    }

    // ✅ Helper: Filter local data only
    function filterLocalDataOnly() {
        const base = getInitialData();
        let filteredFacts = [...(base.factGmvMaxProducts || [])];

        // Apply date range filter từ DateRangePicker
        if (
            dateRangePicker &&
            dateRangePicker.startDate &&
            dateRangePicker.endDate
        ) {
            try {
                const fromDate = new Date(dateRangePicker.startDate);
                const toDate = new Date(dateRangePicker.endDate);

                if (!isNaN(fromDate.getTime()) && !isNaN(toDate.getTime())) {
                    filteredFacts = filteredFacts.filter((fact) => {
                        if (!fact.date) return true;
                        const factDate = new Date(fact.date);
                        if (isNaN(factDate.getTime())) return true;
                        return factDate >= fromDate && factDate <= toDate;
                    });
                }
            } catch (error) {
                console.error('❌ Error applying date filter:', error);
            }
        }

        // Apply other filters
        filteredFacts = filteredFacts.filter((f) => {
            // Business center filter
            if (filterState.businessCenters.length > 0) {
                const id = f.dimBusinessCenterId || f.businessCenterId;
                if (!filterState.businessCenters.includes(id)) return false;
            }

            // Shop filter
            if (filterState.shops.length > 0) {
                const id = f.dimStoreId || f.storeId;
                if (!filterState.shops.includes(id)) return false;
            }

            // Keyword search across common fields
            if (filterState.keyword && filterState.keyword.trim().length > 0) {
                const kw = filterState.keyword.trim().toLowerCase();
                const hay = [
                    f.productName,
                    f.productId,
                    f.sku,
                    f.category,
                    f.campaignName,
                    f.storeName,
                ]
                    .filter(Boolean)
                    .map((x) => String(x).toLowerCase())
                    .join(' ');
                if (!hay.includes(kw)) return false;
            }

            return true;
        });

        const payload = {
            ...base,
            factGmvMaxProducts: filteredFacts,
        };

        if (window.tiktokGmvMaxProductPivotTable) {
            showLoading && showLoading('Đang áp dụng bộ lọc...');

            // ✅ SMART COLUMNS: Update columns based on current date range
            const smartColumns =
                window.tiktokGmvMaxProductPivotTable.getSmartTimeColumns();
            window.tiktokGmvMaxProductPivotTable.pivotTableObj.dataSourceSettings.columns =
                smartColumns;

            window.tiktokGmvMaxProductPivotTable.refreshData(payload);
            hideLoading && hideLoading();
            showToast &&
                showToast(
                    'success',
                    `Đã áp dụng bộ lọc. Hiển thị ${filteredFacts.length} bản ghi.`
                );
        }
    }

    // Clear filters (now refresh back to original data immediately)
    const clearBtn = document.getElementById('clear-all-filters');
    if (clearBtn) {
        clearBtn.addEventListener('click', async function () {
            // Reset state
            filterState.dateRange = { start: null, end: null };
            filterState.quickDate = null;
            filterState.businessCenters = [];
            filterState.shops = [];
            // ✅ Removed: Creative types, product statuses, categories, and performance filters
            filterState.keyword = '';

            // Reset UI controls
            if (dateRangePicker) {
                try {
                    dateRangePicker.startDate = null;
                    dateRangePicker.endDate = null;
                    dateRangePicker.value = null;
                    dateRangePicker.dataBind && dateRangePicker.dataBind();
                } catch (e) {
                    console.error('❌ Không thể reset DateRangePicker:', e);
                }
            }
            if (quickDateDropdown) {
                try {
                    quickDateDropdown.value = null;
                    quickDateDropdown.dataBind && quickDateDropdown.dataBind();
                } catch (e) {
                    console.error('❌ Không thể reset QuickDate dropdown:', e);
                }
            }
            if (bcMulti) bcMulti.value = [];
            if (shopMulti) shopMulti.value = [];
            if (keywordInput) keywordInput.value = '';

            // Ensure initial dataset exists
            if (
                !window.initialGmvMaxProductData &&
                window.loadProductDataForFilters
            ) {
                try {
                    await window.loadProductDataForFilters();
                } catch (e) {
                    console.error(
                        '❌ Không thể tải dữ liệu gốc cho Product:',
                        e
                    );
                }
            }

            // Refresh pivot to original data immediately
            if (
                window.tiktokGmvMaxProductPivotTable &&
                window.initialGmvMaxProductData
            ) {
                showLoading && showLoading('Đang khôi phục dữ liệu...');
                window.tiktokGmvMaxProductPivotTable.refreshData(
                    window.initialGmvMaxProductData
                );
                hideLoading && hideLoading();
            }

            showToast && showToast('success', 'Đã xóa tất cả bộ lọc');
        });
    }

    // ✅ OPTIMIZED: Quick date filter function - cập nhật DateRangePicker
    function setQuickDateFilter(period) {
        const range =
            window.sharedDateHelper && window.sharedDateHelper.getQuickDateRange
                ? window.sharedDateHelper.getQuickDateRange(period)
                : (function () {
                      const endDate = new Date();
                      let startDate = new Date();
                      if (period === 'today') {
                          // startDate already today
                      } else if (period === '7d') {
                          startDate.setDate(startDate.getDate() - 7);
                      } else if (period === '30d') {
                          startDate.setDate(startDate.getDate() - 30);
                      } else if (period === '90d') {
                          startDate.setDate(startDate.getDate() - 90);
                      } else {
                          startDate.setDate(startDate.getDate() - 7);
                      }
                      return { startDate: startDate, endDate: endDate };
                  })();

        if (dateRangePicker) {
            try {
                if (
                    range &&
                    range.startDate &&
                    range.endDate &&
                    !isNaN(range.startDate.getTime()) &&
                    !isNaN(range.endDate.getTime())
                ) {
                    dateRangePicker.startDate = range.startDate;
                    dateRangePicker.endDate = range.endDate;
                } else {
                    console.error('❌ Invalid dates for quick filter');
                }
            } catch (error) {
                console.error('❌ Error setting quick date filter:', error);
            }
        }
    }
});
