using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Repositories
{
    /// <summary>
    /// Repository interface cho GMV Max Campaigns
    /// </summary>
    public interface IRawGmvMaxCampaignsRepository : IRepository<RawGmvMaxCampaignsEntity, Guid>
    {
        /// <summary>
        /// Lấy danh sách GMV Max Campaigns với bộ lọc
        /// </summary>
        /// <param name="filterText">Từ khóa tìm kiếm</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <param name="operationStatus">Trạng thái hoạt động</param>
        /// <param name="objectiveType"><PERSON>ại mục tiêu</param>
        /// <param name="storeId">ID cửa hàng</param>
        /// <param name="shoppingAdsType">Loại GMV Max Campaign</param>
        /// <param name="optimizationGoal">Mục tiêu tối ưu</param>
        /// <param name="deepBidType">Chiến lược đấu giá</param>
        /// <param name="scheduleType">Loại lịch trình</param>
        /// <param name="roiProtectionEnabled">Có bật bảo vệ ROI</param>
        /// <param name="createTimeFrom">Thời gian tạo từ</param>
        /// <param name="createTimeTo">Thời gian tạo đến</param>
        /// <param name="budgetFrom">Ngân sách từ</param>
        /// <param name="budgetTo">Ngân sách đến</param>
        /// <param name="syncedAtFrom">Thời gian đồng bộ từ</param>
        /// <param name="syncedAtTo">Thời gian đồng bộ đến</param>
        /// <param name="sorting">Sắp xếp</param>
        /// <param name="maxResultCount">Số lượng kết quả tối đa</param>
        /// <param name="skipCount">Số lượng bỏ qua</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách GMV Max Campaigns</returns>
        Task<List<RawGmvMaxCampaignsEntity>> GetListAsync(
            string? filterText = null,
            string? advertiserId = null,
            string? campaignId = null,
            string? operationStatus = null,
            string? objectiveType = null,
            string? storeId = null,
            GmvMaxShoppingAdsType? shoppingAdsType = null,
            OptimizationGoal? optimizationGoal = null,
            DeepBidType? deepBidType = null,
            GmvMaxScheduleType? scheduleType = null,
            bool? roiProtectionEnabled = null,
            DateTime? createTimeFrom = null,
            DateTime? createTimeTo = null,
            decimal? budgetFrom = null,
            decimal? budgetTo = null,
            DateTime? syncedAtFrom = null,
            DateTime? syncedAtTo = null,
            string? sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Đếm số lượng GMV Max Campaigns với bộ lọc
        /// </summary>
        /// <param name="filterText">Từ khóa tìm kiếm</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <param name="operationStatus">Trạng thái hoạt động</param>
        /// <param name="objectiveType">Loại mục tiêu</param>
        /// <param name="storeId">ID cửa hàng</param>
        /// <param name="shoppingAdsType">Loại GMV Max Campaign</param>
        /// <param name="optimizationGoal">Mục tiêu tối ưu</param>
        /// <param name="deepBidType">Chiến lược đấu giá</param>
        /// <param name="scheduleType">Loại lịch trình</param>
        /// <param name="roiProtectionEnabled">Có bật bảo vệ ROI</param>
        /// <param name="createTimeFrom">Thời gian tạo từ</param>
        /// <param name="createTimeTo">Thời gian tạo đến</param>
        /// <param name="budgetFrom">Ngân sách từ</param>
        /// <param name="budgetTo">Ngân sách đến</param>
        /// <param name="syncedAtFrom">Thời gian đồng bộ từ</param>
        /// <param name="syncedAtTo">Thời gian đồng bộ đến</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Số lượng GMV Max Campaigns</returns>
        Task<long> GetCountAsync(
            string? filterText = null,
            string? advertiserId = null,
            string? campaignId = null,
            string? operationStatus = null,
            string? objectiveType = null,
            string? storeId = null,
            GmvMaxShoppingAdsType? shoppingAdsType = null,
            OptimizationGoal? optimizationGoal = null,
            DeepBidType? deepBidType = null,
            GmvMaxScheduleType? scheduleType = null,
            bool? roiProtectionEnabled = null,
            DateTime? createTimeFrom = null,
            DateTime? createTimeTo = null,
            decimal? budgetFrom = null,
            decimal? budgetTo = null,
            DateTime? syncedAtFrom = null,
            DateTime? syncedAtTo = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Tìm GMV Max Campaign theo CampaignId
        /// </summary>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>GMV Max Campaign hoặc null nếu không tìm thấy</returns>
        Task<RawGmvMaxCampaignsEntity?> FindByCampaignIdAsync(
            string campaignId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Tìm tất cả GMV Max Campaigns theo AdvertiserId
        /// </summary>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách GMV Max Campaigns</returns>
        Task<List<RawGmvMaxCampaignsEntity>> GetByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Tìm tất cả GMV Max Campaigns theo StoreId
        /// </summary>
        /// <param name="storeId">ID cửa hàng</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách GMV Max Campaigns</returns>
        Task<List<RawGmvMaxCampaignsEntity>> GetByStoreIdAsync(
            string storeId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra xem CampaignId đã tồn tại chưa
        /// </summary>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <param name="excludeId">ID cần loại trừ (cho trường hợp update)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>True nếu đã tồn tại, False nếu chưa</returns>
        Task<bool> IsCampaignIdExistsAsync(
            string campaignId,
            Guid? excludeId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách GMV Max Campaigns theo danh sách Campaign IDs
        /// </summary>
        /// <param name="campaignIds">Danh sách Campaign IDs</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách GMV Max Campaigns</returns>
        Task<List<RawGmvMaxCampaignsEntity>> GetByCampaignIdsAsync(
            List<string> campaignIds,
            CancellationToken cancellationToken = default);
        /// <summary>
        /// Lấy danh sách GMV Max Campaigns theo AdAccount Id và trạng thái
        /// </summary>
        /// <param name="advertiserId"></param>
        /// <param name="status"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<List<RawGmvMaxCampaignsEntity>> GetByAdvertiserIdAndOperationStatusAsync(
            string advertiserId, string status,
            CancellationToken cancellationToken = default);
    }
} 