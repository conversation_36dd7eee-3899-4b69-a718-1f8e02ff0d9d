using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TikTok.Entities;
using TikTok.EntityFrameworkCore;
using TikTok.Enums;
using TikTok.Repositories;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// Repository implementation cho GMV Max Campaigns
    /// </summary>
    public class RawGmvMaxCampaignsRepository : EfCoreRepository<TikTokDbContext, RawGmvMaxCampaignsEntity, Guid>, IRawGmvMaxCampaignsRepository
    {
        public RawGmvMaxCampaignsRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<List<RawGmvMaxCampaignsEntity>> GetListAsync(
            string? filterText = null,
            string? advertiserId = null,
            string? campaignId = null,
            string? operationStatus = null,
            string? objectiveType = null,
            string? storeId = null,
            GmvMaxShoppingAdsType? shoppingAdsType = null,
            OptimizationGoal? optimizationGoal = null,
            DeepBidType? deepBidType = null,
            GmvMaxScheduleType? scheduleType = null,
            bool? roiProtectionEnabled = null,
            DateTime? createTimeFrom = null,
            DateTime? createTimeTo = null,
            decimal? budgetFrom = null,
            decimal? budgetTo = null,
            DateTime? syncedAtFrom = null,
            DateTime? syncedAtTo = null,
            string? sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            query = ApplyFilter(query, filterText, advertiserId, campaignId, operationStatus, objectiveType, storeId,
                shoppingAdsType, optimizationGoal, deepBidType, scheduleType, roiProtectionEnabled,
                createTimeFrom, createTimeTo, budgetFrom, budgetTo, syncedAtFrom, syncedAtTo);

            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? nameof(RawGmvMaxCampaignsEntity.ModifyTime) + " desc" : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> GetCountAsync(
            string? filterText = null,
            string? advertiserId = null,
            string? campaignId = null,
            string? operationStatus = null,
            string? objectiveType = null,
            string? storeId = null,
            GmvMaxShoppingAdsType? shoppingAdsType = null,
            OptimizationGoal? optimizationGoal = null,
            DeepBidType? deepBidType = null,
            GmvMaxScheduleType? scheduleType = null,
            bool? roiProtectionEnabled = null,
            DateTime? createTimeFrom = null,
            DateTime? createTimeTo = null,
            decimal? budgetFrom = null,
            decimal? budgetTo = null,
            DateTime? syncedAtFrom = null,
            DateTime? syncedAtTo = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            query = ApplyFilter(query, filterText, advertiserId, campaignId, operationStatus, objectiveType, storeId,
                shoppingAdsType, optimizationGoal, deepBidType, scheduleType, roiProtectionEnabled,
                createTimeFrom, createTimeTo, budgetFrom, budgetTo, syncedAtFrom, syncedAtTo);

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<RawGmvMaxCampaignsEntity?> FindByCampaignIdAsync(
            string campaignId,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.FirstOrDefaultAsync(x => x.CampaignId == campaignId, GetCancellationToken(cancellationToken));
        }

        public async Task<List<RawGmvMaxCampaignsEntity>> GetByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.Where(x => x.AdvertiserId == advertiserId)
                .OrderByDescending(x => x.ModifyTime)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }
        public async Task<List<RawGmvMaxCampaignsEntity>> GetByAdvertiserIdAndOperationStatusAsync(
            string advertiserId,string status,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.Where(x => x.AdvertiserId == advertiserId&&x.OperationStatus==status)
                .OrderByDescending(x => x.ModifyTime)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }
        public async Task<List<RawGmvMaxCampaignsEntity>> GetByStoreIdAsync(
            string storeId,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            return await query.Where(x => x.StoreId == storeId)
                .OrderByDescending(x => x.ModifyTime)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<bool> IsCampaignIdExistsAsync(
            string campaignId,
            Guid? excludeId = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();
            query = query.Where(x => x.CampaignId == campaignId);

            if (excludeId.HasValue)
            {
                query = query.Where(x => x.Id != excludeId.Value);
            }

            return await query.AnyAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<RawGmvMaxCampaignsEntity>> GetByCampaignIdsAsync(
            List<string> campaignIds,
            CancellationToken cancellationToken = default)
        {
            if (campaignIds == null || !campaignIds.Any())
            {
                return new List<RawGmvMaxCampaignsEntity>();
            }

            var query = await WithDetailsAsync(x => x.Items, x => x.Identities, x => x.CustomAnchorVideos);
            return await query.Where(x => campaignIds.Contains(x.CampaignId))
                .OrderByDescending(x => x.ModifyTime)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        private IQueryable<RawGmvMaxCampaignsEntity> ApplyFilter(
            IQueryable<RawGmvMaxCampaignsEntity> query,
            string? filterText = null,
            string? advertiserId = null,
            string? campaignId = null,
            string? operationStatus = null,
            string? objectiveType = null,
            string? storeId = null,
            GmvMaxShoppingAdsType? shoppingAdsType = null,
            OptimizationGoal? optimizationGoal = null,
            DeepBidType? deepBidType = null,
            GmvMaxScheduleType? scheduleType = null,
            bool? roiProtectionEnabled = null,
            DateTime? createTimeFrom = null,
            DateTime? createTimeTo = null,
            decimal? budgetFrom = null,
            decimal? budgetTo = null,
            DateTime? syncedAtFrom = null,
            DateTime? syncedAtTo = null)
        {
            if (!string.IsNullOrWhiteSpace(filterText))
            {
                query = query.Where(x =>
                    x.CampaignName.Contains(filterText) ||
                    x.CampaignId.Contains(filterText) ||
                    x.AdvertiserId.Contains(filterText) ||
                    x.StoreId.Contains(filterText) ||
                    (x.ObjectiveType != null && x.ObjectiveType.Contains(filterText)) ||
                    (x.OperationStatus != null && x.OperationStatus.Contains(filterText)));
            }

            if (!string.IsNullOrWhiteSpace(advertiserId))
            {
                query = query.Where(x => x.AdvertiserId.Contains(advertiserId));
            }

            if (!string.IsNullOrWhiteSpace(campaignId))
            {
                query = query.Where(x => x.CampaignId.Contains(campaignId));
            }

            if (!string.IsNullOrWhiteSpace(operationStatus))
            {
                query = query.Where(x => x.OperationStatus.Contains(operationStatus));
            }

            if (!string.IsNullOrWhiteSpace(objectiveType))
            {
                query = query.Where(x => x.ObjectiveType.Contains(objectiveType));
            }

            if (!string.IsNullOrWhiteSpace(storeId))
            {
                query = query.Where(x => x.StoreId.Contains(storeId));
            }

            if (shoppingAdsType.HasValue)
            {
                query = query.Where(x => x.ShoppingAdsType == shoppingAdsType.Value);
            }

            if (optimizationGoal.HasValue)
            {
                query = query.Where(x => x.OptimizationGoal == optimizationGoal.Value);
            }

            if (deepBidType.HasValue)
            {
                query = query.Where(x => x.DeepBidType == deepBidType.Value);
            }

            if (scheduleType.HasValue)
            {
                query = query.Where(x => x.ScheduleType == scheduleType.Value);
            }

            if (roiProtectionEnabled.HasValue)
            {
                query = query.Where(x => x.RoiProtectionEnabled == roiProtectionEnabled.Value);
            }

            if (createTimeFrom.HasValue)
            {
                query = query.Where(x => x.CreateTime >= createTimeFrom.Value);
            }

            if (createTimeTo.HasValue)
            {
                query = query.Where(x => x.CreateTime <= createTimeTo.Value);
            }

            if (budgetFrom.HasValue)
            {
                query = query.Where(x => x.Budget >= budgetFrom.Value);
            }

            if (budgetTo.HasValue)
            {
                query = query.Where(x => x.Budget <= budgetTo.Value);
            }

            if (syncedAtFrom.HasValue)
            {
                query = query.Where(x => x.SyncedAt >= syncedAtFrom.Value);
            }

            if (syncedAtTo.HasValue)
            {
                query = query.Where(x => x.SyncedAt <= syncedAtTo.Value);
            }

            return query;
        }
    }
}