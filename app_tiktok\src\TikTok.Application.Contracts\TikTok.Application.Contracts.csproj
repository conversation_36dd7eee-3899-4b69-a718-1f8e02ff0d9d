﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFrameworks>net8.0</TargetFrameworks>
		<Nullable>enable</Nullable>
		<RootNamespace>TikTok</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\TikTok.Domain.Shared\TikTok.Domain.Shared.csproj" />
		<ProjectReference Include="..\TikTok.Domain\TikTok.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Volo.Abp.ObjectExtending" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="8.1.4" />
		<PackageReference Include="Tsp.Zalo.Application.Contracts" Version="1.0.0-prerelease-5916" />
    <PackageReference Include="Tsp.Module.Notifications.Application.Contracts" Version="1.0.17" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Syncs\" />
	</ItemGroup>

</Project>
