/**
 * Video Tab Manager
 * Handles Video tab functionality with status cards and problematic videos table
 */

class VideoTabManager {
    constructor(config, dataAggregator) {
        this.config = config;
        this.dataAggregator = dataAggregator;
        this.videoStatusCards = null;
        this.problematicVideosGrid = null;

        // ✅ CONSTANTS: Only these 4 problematic statuses are allowed in UI
        this.ALLOWED_STATUSES = [3, 5, 6, 7]; // NOT_DELIVERYIN, EXCLUDED, UNAVAILABLE, REJECTED
        this.currentFilters = {
            fromDate: config.dateRange.from,
            toDate: config.dateRange.to,
            campaignIds: [],
            searchText: '',
            creativeDeliveryStatuses: [3, 5, 6, 7], // Default: problematic statuses
            skipCount: 0,
            maxResultCount: 20,
            sorting: 'date desc',
        };

        // Pagination state
        this.paginationState = {
            currentPage: 1,
            pageSize: 20,
            totalCount: 0,
            totalPages: 0,
        };

        this.isInitialized = false;
        this.isApplyingFilters = false;
    }

    /**
     * Initialize Video Tab
     */
    async init() {
        try {
            // Setup filter components
            this.setupFilterComponents();

            // Setup event listeners
            this.setupEventListeners();

            // Initialize Video Status Cards component with reference to this manager
            this.videoStatusCards = new VideoStatusCardsComponent(this);

            // Initialize Syncfusion Grid for problematic videos
            this.initProblematicVideosGrid();

            // Load campaign IDs for filter
            await this.loadCampaignIds();

            this.isInitialized = true;
        } catch (error) {
            console.error('❌ Error initializing Video Tab:', error);
            throw error;
        }
    }

    /**
     * Load video data
     */
    async loadData(filters = null) {
        try {
            if (!this.isInitialized) {
                await this.init();
            }

            // Update filters if provided
            if (filters) {
                this.currentFilters = { ...this.currentFilters, ...filters };
            }

            // Show loading states
            this.showLoadingState();

            // Load video status analysis with total counts (separate from grid data)
            await this.loadVideoStatusAnalysis();

            // Refresh grid with current filters (Syncfusion Grid handles its own data)
            this.refreshProblematicVideosGrid();

            // Hide loading states
            this.hideLoadingState();
        } catch (error) {
            console.error('❌ Error loading Video Tab data:', error);
            this.hideLoadingState();
            this.showErrorMessage('Lỗi tải dữ liệu video: ' + error.message);
        }
    }

    /**
     * Refresh Syncfusion Grid
     */
    /**
     * Apply filters for problematic videos
     */
    applyProblematicVideoFilters() {
        if (!this.problematicVideosGrid) return;

        // Create base query
        let query = new window.ej.data.Query();

        // ✅ FIX: Build all predicates first, then combine with proper precedence
        let allPredicates = [];

        // 1. Status predicate (use current filter statuses)
        const statusesToFilter = this.currentFilters
            .creativeDeliveryStatuses || [3, 5, 6, 7];
        const statusEnumValues = statusesToFilter.map((status) =>
            this.numberToStatusCode(status)
        );

        let statusPredicate = new window.ej.data.Predicate(
            'CreativeDeliveryStatus',
            'equal',
            statusEnumValues[0]
        );
        for (let i = 1; i < statusEnumValues.length; i++) {
            statusPredicate = statusPredicate.or(
                'CreativeDeliveryStatus',
                'equal',
                statusEnumValues[i]
            );
        }
        allPredicates.push(statusPredicate);

        // 2. Date range predicates
        if (this.currentFilters.fromDate) {
            const fromDateTime = new Date(
                this.currentFilters.fromDate + 'T00:00:00.000Z'
            );
            allPredicates.push(
                new window.ej.data.Predicate(
                    'Date',
                    'greaterthanorequal',
                    fromDateTime
                )
            );
        }
        if (this.currentFilters.toDate) {
            const toDateTime = new Date(
                this.currentFilters.toDate + 'T23:59:59.999Z'
            );
            allPredicates.push(
                new window.ej.data.Predicate(
                    'Date',
                    'lessthanorequal',
                    toDateTime
                )
            );
        }

        // 3. Campaign predicate
        if (
            this.currentFilters.campaignIds &&
            this.currentFilters.campaignIds.length > 0
        ) {
            let campaignPredicate = new window.ej.data.Predicate(
                'CampaignId',
                'equal',
                this.currentFilters.campaignIds[0]
            );
            for (let i = 1; i < this.currentFilters.campaignIds.length; i++) {
                campaignPredicate = campaignPredicate.or(
                    'CampaignId',
                    'equal',
                    this.currentFilters.campaignIds[i]
                );
            }
            allPredicates.push(campaignPredicate);
        }

        // 4. Search predicate
        if (
            this.currentFilters.searchText &&
            this.currentFilters.searchText.trim()
        ) {
            const searchText = this.currentFilters.searchText.trim();
            const searchPredicate = new window.ej.data.Predicate(
                'Title',
                'contains',
                searchText
            )
                .or('CampaignId', 'contains', searchText)
                .or('TtAccountName', 'contains', searchText);
            allPredicates.push(searchPredicate);
        }

        // ✅ FIX: Combine all predicates with AND (proper precedence)
        if (allPredicates.length > 0) {
            let combinedPredicate = allPredicates[0];
            for (let i = 1; i < allPredicates.length; i++) {
                combinedPredicate = combinedPredicate.and(allPredicates[i]);
            }
            query = query.where(combinedPredicate);
        }

        // ✅ DEBUG: Log OData query for comparison
        console.log('🔍 OData Query for Grid:', query);
        console.log('🔍 OData Filters:', this.currentFilters);

        // Apply query to grid
        this.problematicVideosGrid.query = query;
    }

    refreshProblematicVideosGrid() {
        if (this.problematicVideosGrid) {
            this.applyProblematicVideoFilters();
        }
    }

    /**
     * Apply all filters - both statistics and grid data (single API call approach)
     */
    async applyAllFilters() {
        try {
            // Prevent multiple simultaneous calls
            if (this.isApplyingFilters) {
                console.log(
                    '⏳ Filter application already in progress, skipping...'
                );
                return;
            }

            this.isApplyingFilters = true;

            // Show loading state
            this.showLoadingState();

            // ✅ Single coordinated approach: Load data once and update both components

            // 1. Refresh video statistics with current filters
            await this.loadVideoStatusAnalysis();

            // 2. Apply filters to problematic videos grid (no additional API call)
            this.applyProblematicVideoFilters();

            // Hide loading state
            this.hideLoadingState();
        } catch (error) {
            console.error('❌ Error applying all filters:', error);
            this.showErrorMessage('Lỗi áp dụng bộ lọc: ' + error.message);
            this.hideLoadingState();
        } finally {
            this.isApplyingFilters = false;
        }
    }

    /**
     * Convert numeric status to OData enum code
     */
    numberToStatusCode(n) {
        switch (+n) {
            case 0:
                return 'IN_QUEUE';
            case 1:
                return 'LEARNING';
            case 2:
                return 'DELIVERING';
            case 3:
                return 'NOT_DELIVERYIN';
            case 4:
                return 'AUTHORIZATION_NEEDED';
            case 5:
                return 'EXCLUDED';
            case 6:
                return 'UNAVAILABLE';
            case 7:
                return 'REJECTED';
            default:
                return String(n);
        }
    }

    /**
     * Load video status analysis cards (with total counts, not paginated)
     */
    async loadVideoStatusAnalysis(videoData) {
        try {
            // Get total video statistics (without pagination) for accurate counts
            const totalVideoStats = await this.getTotalVideoStatistics();

            // Render video status cards with total counts
            await this.videoStatusCards.render(
                totalVideoStats,
                'video-status-cards-container'
            );

            // Update problematic videos count with total count
            this.updateProblematicVideosCount(
                totalVideoStats.problematicVideos.length
            );
        } catch (error) {
            console.error('❌ Error loading video status analysis:', error);
            throw error;
        }
    }

    /**
     * Get total video statistics using dedicated API endpoint
     */
    async getTotalVideoStatistics() {
        try {
            // Build query parameters for statistics API (matching OData filters exactly)
            const params = new URLSearchParams();

            if (this.currentFilters.searchText) {
                params.append('searchText', this.currentFilters.searchText);
            }
            if (this.currentFilters.fromDate) {
                params.append('fromDate', this.currentFilters.fromDate);
            }
            if (this.currentFilters.toDate) {
                params.append('toDate', this.currentFilters.toDate);
            }
            if (
                this.currentFilters.campaignIds &&
                this.currentFilters.campaignIds.length > 0
            ) {
                this.currentFilters.campaignIds.forEach((id) =>
                    params.append('campaignIds', id)
                );
            }

            // ✅ NOTE: Don't filter statuses for statistics - we need ALL statuses for cards
            // Only the problematic count will be calculated from the full data

            // Use dedicated API endpoint for video statistics
            const apiUrl = `/api/raw-gmv-max-product-creative-report/video-status-statistics?${params}`;

            // ✅ DEBUG: Log API call for statistics
            console.log('📊 Statistics API URL:', apiUrl);
            console.log('📊 Statistics Filters:', this.currentFilters);

            const response = await fetch(apiUrl, {
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(
                    `HTTP ${response.status}: ${response.statusText}`
                );
            }

            const statisticsData = await response.json();

            // ✅ ENSURE UI only shows allowed problematic statuses
            // Transform API response and filter to only allowed statuses
            const statusCards =
                statisticsData.statusCards
                    ?.filter((card) =>
                        this.ALLOWED_STATUSES.includes(card.status)
                    )
                    ?.map((card) => ({
                        status: card.status,
                        statusKey: this.numberToStatusCode(card.status),
                        name: card.name,
                        icon: card.icon,
                        color: card.color,
                        count: card.count,
                        items: [], // Not needed for cards, only count matters
                    })) || [];

            return {
                statusCards,
                problematicVideos: [], // Array not needed for statistics
                summary: {
                    total: statisticsData.summary?.total || 0,
                    problematicCount:
                        statisticsData.summary?.problematicCount || 0,
                    problematicPercentage:
                        statisticsData.summary?.problematicPercentage || 0,
                },
            };
        } catch (error) {
            console.error('❌ Error getting total video statistics:', error);
            // Fallback to empty statistics
            return {
                statusCards: [],
                problematicVideos: [],
                summary: {
                    total: 0,
                    problematicCount: 0,
                    problematicPercentage: 0,
                },
            };
        }
    }

    /**
     * Initialize Syncfusion Grid for problematic videos
     */
    initProblematicVideosGrid() {
        try {
            if (!window.ej || !window.ej.grids) {
                console.error('❌ Syncfusion Grid not available');
                return;
            }

            // Create grid instance
            this.problematicVideosGrid = new window.ej.grids.Grid({
                dataSource: new window.ej.data.DataManager({
                    url: '/odata/RawGmvMaxProductCreativeReports',
                    adaptor: new window.ej.data.ODataV4Adaptor(),
                    crossDomain: false,
                }),
                allowPaging: true,
                pageSettings: { pageSize: 20, pageSizes: [10, 20, 50] },
                allowSorting: true,
                // ✅ Removed: toolbar search and searchSettings
                // ✅ Default sorting by Date descending (newest first)
                sortSettings: {
                    columns: [{ field: 'Date', direction: 'Descending' }],
                },
                dataBound: () => {
                    this.updateProblematicVideosCount(
                        this.problematicVideosGrid.pageSettings
                            ?.totalRecordsCount || 0
                    );
                },
                columns: [
                    {
                        field: 'Title',
                        headerText: 'Tiêu đề Video',
                        width: 250,
                        template:
                            '<div class="video-title-cell" title="${Title}">${Title}</div>',
                    },
                    {
                        field: 'TtAccountName',
                        headerText: 'Kênh',
                        width: 180,
                        template:
                            '<div class="text-primary video-channel-cell" title="${TtAccountName}"><i class="fab fa-tiktok me-1"></i>${TtAccountName}</div>',
                    },
                    {
                        field: 'CampaignId',
                        headerText: 'Campaign',
                        width: 150,
                        template:
                            '<div class="video-campaign-cell" title="Campaign ID: ${CampaignId}">${CampaignId}</div>',
                    },
                    {
                        field: 'CreativeDeliveryStatus',
                        headerText: 'Trạng thái Video',
                        width: 150,
                        template:
                            '${getVideoStatusBadge(CreativeDeliveryStatus)}',
                    },
                    {
                        field: 'Date',
                        headerText: 'Ngày',
                        width: 120,
                        type: 'date',
                        format: 'dd/MM/yyyy HH:mm',
                    },
                    {
                        headerText: 'Thao tác',
                        width: 100,
                        template: `
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="videoTabManager.editVideo('\${CampaignId}', '\${ItemId}')" title="Chỉnh sửa video">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="videoTabManager.markAsProcessed('\${CampaignId}', '\${ItemId}')" title="Đánh dấu đã xử lý">
                                    <i class="fas fa-check-circle"></i>
                                </button>
                            </div>
                        `,
                    },
                ],
            });

            // Append to container
            this.problematicVideosGrid.appendTo('#problematic-videos-grid');

            // Make available globally for template functions
            window.videoTabManager = this;

            // Apply initial filter for problematic videos
            this.applyProblematicVideoFilters();
        } catch (error) {
            console.error(
                '❌ Error initializing problematic videos grid:',
                error
            );
            throw error;
        }
    }

    /**
     * Get status information
     */
    getStatusInfo(status) {
        const statusMap = {
            0: {
                name: 'In Queue',
                icon: '<i class="fas fa-hourglass-half"></i>',
                color: 'warning',
            },
            1: {
                name: 'Learning',
                icon: '<i class="fas fa-graduation-cap"></i>',
                color: 'info',
            },
            2: {
                name: 'Delivering',
                icon: '<i class="fas fa-broadcast-tower"></i>',
                color: 'success',
            },
            3: {
                name: 'Not Delivery',
                icon: '<i class="fas fa-pause-circle"></i>',
                color: 'danger',
            },
            4: {
                name: 'Authorization Needed',
                icon: '<i class="fas fa-user-check"></i>',
                color: 'warning',
            },
            5: {
                name: 'Excluded',
                icon: '<i class="fas fa-times-circle"></i>',
                color: 'danger',
            },
            6: {
                name: 'Unavailable',
                icon: '<i class="fas fa-exclamation-circle"></i>',
                color: 'secondary',
            },
            7: {
                name: 'Rejected',
                icon: '<i class="fas fa-ban"></i>',
                color: 'danger',
            },
        };

        return (
            statusMap[status] || {
                name: `Status ${status}`,
                icon: '<i class="fas fa-question-circle"></i>',
                color: 'secondary',
            }
        );
    }

    /**
     * Setup filter components
     */
    setupFilterComponents() {
        try {
            // Initialize Syncfusion DateRangePicker
            const dateRangePicker = new ej.calendars.DateRangePicker({
                startDate: new Date(this.currentFilters.fromDate),
                endDate: new Date(this.currentFilters.toDate),
                format: 'dd/MM/yyyy',
                placeholder: 'Chọn khoảng thời gian',
                change: (args) => {
                    if (args.startDate && args.endDate) {
                        this.currentFilters.fromDate = args.startDate
                            .toISOString()
                            .split('T')[0];
                        this.currentFilters.toDate = args.endDate
                            .toISOString()
                            .split('T')[0];
                    }
                },
            });
            dateRangePicker.appendTo('#video-date-range-picker');

            // Initialize Quick Date Dropdown (copy from existing screens)
            const quickDateOptions = [
                { text: 'Chọn khoảng', value: '' },
                { text: 'Hôm nay', value: 'today' },
                { text: '7 ngày', value: '7d' },
                { text: '30 ngày', value: '30d' },
                { text: 'Quý này', value: '90d' },
            ];

            const quickDateDropdown = new ej.dropdowns.DropDownList({
                dataSource: quickDateOptions,
                fields: { text: 'text', value: 'value' },
                placeholder: 'Lọc nhanh',
                change: (args) => {
                    if (args.value) {
                        this.setQuickDateFilter(
                            args.value,
                            'video-date-range-picker'
                        );
                    }
                },
            });
            quickDateDropdown.appendTo('#video-quick-date-dropdown');

            // Initialize Campaign MultiSelect (will be populated with data)
            const campaignMultiSelect = new ej.dropdowns.MultiSelect({
                placeholder: 'Chọn campaign',
                mode: 'CheckBox',
                showCheckBox: true,
                showSelectAll: true,
                enableSelectionOrder: false,
                change: (args) => {
                    this.currentFilters.campaignIds = args.value || [];
                },
            });
            campaignMultiSelect.appendTo('#video-campaign-multiselect');

            // Initialize Text Search
            const keywordSearch = document.getElementById(
                'video-keyword-search'
            );
            if (keywordSearch) {
                keywordSearch.addEventListener('input', (e) => {
                    this.currentFilters.searchText = e.target.value || '';
                });

                // Add Enter key support for quick apply
                keywordSearch.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.applyAllFilters();
                    }
                });
            }
        } catch (error) {
            console.error(
                '❌ Error setting up video filter components:',
                error
            );
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Apply filters button - calls both statistics and grid APIs
        const applyFiltersBtn = document.getElementById('apply-video-filters');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyAllFilters();
            });
        }

        // Clear filters button
        const clearFiltersBtn = document.getElementById('clear-video-filters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        // Refresh problematic videos button
        const refreshBtn = document.getElementById(
            'refresh-problematic-videos'
        );
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshTable();
            });
        }
    }

    /**
     * Setup table interactions
     */
    setupTableInteractions() {
        // Make this available globally for onclick handlers
        window.videoTabManager = this;
    }

    /**
     * Load campaign data for filter dropdown using new API
     */
    async loadCampaignIds() {
        try {
            // Load campaigns from new API
            const response = await fetch('/api/dim-campaigns?format=simple');

            if (!response.ok) {
                console.warn('Failed to load campaigns:', response.statusText);
                return;
            }

            const campaigns = await response.json();

            // Update campaign multiselect
            const campaignMultiSelect = document.querySelector(
                '#video-campaign-multiselect'
            );
            if (
                campaignMultiSelect &&
                campaignMultiSelect.ej2_instances &&
                campaigns.length > 0
            ) {
                const multiSelectObj = campaignMultiSelect.ej2_instances[0];
                multiSelectObj.dataSource = campaigns; // API already returns {text, value} format
                multiSelectObj.dataBind();
            } else if (campaigns.length === 0) {
                // Show empty state
                const multiSelectObj = campaignMultiSelect.ej2_instances[0];
                multiSelectObj.dataSource = [
                    { text: 'Không có dữ liệu campaign', value: '' },
                ];
                multiSelectObj.dataBind();
            }
        } catch (error) {
            console.error('❌ Error loading campaigns for video tab:', error);

            // Fallback: Show error state
            const campaignMultiSelect = document.querySelector(
                '#video-campaign-multiselect'
            );
            if (campaignMultiSelect && campaignMultiSelect.ej2_instances) {
                const multiSelectObj = campaignMultiSelect.ej2_instances[0];
                multiSelectObj.dataSource = [
                    { text: 'Lỗi tải dữ liệu', value: '' },
                ];
                multiSelectObj.dataBind();
            }
        }
    }

    /**
     * Clear all filters (can be called programmatically)
     */
    clearFilters() {
        this.currentFilters = {
            fromDate: this.config.dateRange.from,
            toDate: this.config.dateRange.to,
            campaignIds: [],
            searchText: '',
            creativeDeliveryStatuses: [3, 5, 6, 7], // Reset to problematic statuses
            skipCount: 0,
            maxResultCount: 20,
            sorting: 'date desc',
        };

        // Reset pagination state
        this.paginationState = {
            currentPage: 1,
            pageSize: 20,
            totalCount: 0,
            totalPages: 0,
        };

        // Reset filter components
        try {
            // Reset DateRangePicker
            const dateRangePicker = document.getElementById(
                'video-date-range-picker'
            );
            if (dateRangePicker && dateRangePicker.ej2_instances) {
                const dateRangeObj = dateRangePicker.ej2_instances[0];
                dateRangeObj.startDate = new Date(this.config.dateRange.from);
                dateRangeObj.endDate = new Date(this.config.dateRange.to);
            }

            // Reset Quick Date Dropdown
            const quickDateDropdown = document.getElementById(
                'video-quick-date-dropdown'
            );
            if (quickDateDropdown && quickDateDropdown.ej2_instances) {
                const quickDateObj = quickDateDropdown.ej2_instances[0];
                quickDateObj.value = '';
            }

            // Reset Campaign MultiSelect
            const campaignMultiSelect = document.getElementById(
                'video-campaign-multiselect'
            );
            if (campaignMultiSelect && campaignMultiSelect.ej2_instances) {
                const campaignObj = campaignMultiSelect.ej2_instances[0];
                campaignObj.value = [];
            }

            // Reset Text Search
            const keywordSearch = document.getElementById(
                'video-keyword-search'
            );
            if (keywordSearch) {
                keywordSearch.value = '';
            }
        } catch (error) {
            console.error('❌ Error resetting video filter components:', error);
        }

        // Reload data with cleared filters
        this.applyAllFilters();
    }

    /**
     * Filter table by specific status (called from status cards)
     */
    filterByStatus(status) {
        try {
            console.log(`🎯 Applying status filter: ${status}`);

            // Update current filters to include only the selected status
            this.currentFilters.creativeDeliveryStatuses = [status];

            // Reset pagination
            this.currentFilters.skipCount = 0;
            this.paginationState.currentPage = 1;

            // Apply filter immediately to grid
            this.applyProblematicVideoFilters();
        } catch (error) {
            console.error('❌ Error filtering by status:', error);
            this.showErrorMessage('Lỗi lọc theo trạng thái: ' + error.message);
        }
    }

    /**
     * Update problematic videos count badge
     */
    updateProblematicVideosCount(count) {
        const countElement = document.getElementById(
            'problematic-videos-count'
        );
        if (countElement) {
            countElement.textContent = count;
            countElement.className =
                count > 0 ? 'badge bg-warning' : 'badge bg-success';
        }
    }

    /**
     * Video action handlers
     */
    editVideo(campaignId, itemId) {
        console.log(`Edit video - Campaign: ${campaignId}, Item: ${itemId}`);
        // TODO: Implement video edit functionality
        this.showInfoMessage(
            `Chỉnh sửa video Campaign: ${campaignId}, Item: ${itemId}`
        );
    }

    markAsProcessed(campaignId, itemId) {
        if (confirm('Đánh dấu video này đã được xử lý?')) {
            console.log(
                `Mark as processed - Campaign: ${campaignId}, Item: ${itemId}`
            );

            // TODO: Make API call to update video status
            // For now, just show success message and refresh grid
            this.showSuccessMessage('Video đã được đánh dấu là đã xử lý');

            // Refresh grid to update data
            if (this.problematicVideosGrid) {
                this.problematicVideosGrid.refresh();
            }
        }
    }

    refreshTable() {
        if (this.problematicVideosGrid) {
            this.problematicVideosGrid.refresh();
        }
    }

    /**
     * Show loading state
     */
    showLoadingState() {
        const statusContainer = document.getElementById(
            'video-status-cards-container'
        );
        if (statusContainer) {
            statusContainer.innerHTML = `
                <div class="col-12 text-center p-4">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải thống kê video...</p>
                </div>
            `;
        }

        const tableContainer = document.getElementById(
            'problematic-videos-table'
        );
        if (tableContainer) {
            tableContainer.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải danh sách video...</p>
                </div>
            `;
        }
    }

    /**
     * Hide loading state
     */
    hideLoadingState() {
        // Loading states will be replaced by actual content
    }

    /**
     * Show error message
     */
    showErrorMessage(message) {
        const statusContainer = document.getElementById(
            'video-status-cards-container'
        );
        if (statusContainer) {
            statusContainer.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${message}
                    </div>
                </div>
            `;
        }
    }

    /**
     * Show success message
     */
    showSuccessMessage(message) {
        // Show toast notification
        const successToast = document.getElementById('success-toast');
        const successMessage = document.getElementById('success-message');

        if (successToast && successMessage) {
            successMessage.textContent = message;
            const toast = new bootstrap.Toast(successToast);
            toast.show();
        }
    }

    /**
     * Show info message
     */
    showInfoMessage(message) {
        // Show toast notification
        const infoToast = document.getElementById('info-toast');
        const infoMessage = document.getElementById('info-message');

        if (infoToast && infoMessage) {
            infoMessage.textContent = message;
            const toast = new bootstrap.Toast(infoToast);
            toast.show();
        }
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Video tab doesn't have pivot tables, so no resize handling needed
    }

    /**
     * Destroy and cleanup
     */
    destroy() {
        if (this.videoStatusCards) {
            this.videoStatusCards.destroy();
        }

        // Clean up global reference
        if (window.videoTabManager === this) {
            delete window.videoTabManager;
        }
    }

    /**
     * Set quick date filter (copy from existing screens)
     */
    setQuickDateFilter(value, dateRangePickerId) {
        try {
            const dateRangePicker = document.getElementById(dateRangePickerId);
            if (!dateRangePicker || !dateRangePicker.ej2_instances) {
                console.warn('DateRangePicker not found or not initialized');
                return;
            }

            const dateRangeObj = dateRangePicker.ej2_instances[0];
            const today = new Date();
            let startDate, endDate;

            switch (value) {
                case 'today':
                    startDate = new Date(today);
                    endDate = new Date(today);
                    break;
                case '7d':
                    startDate = new Date(
                        today.getTime() - 6 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                case '30d':
                    startDate = new Date(
                        today.getTime() - 29 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                case '90d':
                    startDate = new Date(
                        today.getTime() - 89 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                default:
                    return;
            }

            // Update DateRangePicker
            if (dateRangeObj && startDate && endDate) {
                dateRangeObj.startDate = startDate;
                dateRangeObj.endDate = endDate;

                // Update current filters
                this.currentFilters.fromDate = startDate
                    .toISOString()
                    .split('T')[0];
                this.currentFilters.toDate = endDate
                    .toISOString()
                    .split('T')[0];
            }
        } catch (error) {
            console.error('❌ Error setting quick date filter:', error);
        }
    }

    /**
     * Set campaign filter programmatically (for notification redirects)
     */
    async setCampaignFilter(campaignId) {
        try {
            if (!campaignId) return;

            // Ensure video tab is initialized
            if (!this.isInitialized) {
                await this.init();
            }

            // Set campaign filter
            this.currentFilters.campaignIds = [campaignId];

            // Update the campaign multiselect UI if it exists and is initialized
            const campaignMultiSelect = document.getElementById(
                'video-campaign-multiselect'
            );
            if (campaignMultiSelect && campaignMultiSelect.ej2_instances) {
                const multiSelectInstance =
                    campaignMultiSelect.ej2_instances[0];
                if (multiSelectInstance) {
                    multiSelectInstance.value = [campaignId];
                }
            }

            // Apply filters and reload data
            await this.loadData();

            console.log(`✅ Campaign filter applied: ${campaignId}`);
        } catch (error) {
            console.error('❌ Error setting campaign filter:', error);
            this.showErrorMessage(
                'Lỗi áp dụng bộ lọc campaign: ' + error.message
            );
        }
    }
}

/**
 * Global function for Syncfusion Grid template - Video Status Badge
 */
window.getVideoStatusBadge = function (status) {
    // Handle both numeric and enum string values
    function getCreativeStatusMeta(value) {
        if (value === undefined || value === null || value === '') {
            return { text: 'Unknown', bg: '#F3F4F6', color: '#111827' };
        }

        // Status config with English names (familiar to TikTok users)
        const STATUS_CONFIG = {
            IN_QUEUE: {
                text: 'In Queue',
                bg: '#fff3cd',
                color: '#856404',
            },
            LEARNING: {
                text: 'Learning',
                bg: '#cff4fc',
                color: '#055160',
            },
            DELIVERING: {
                text: 'Delivering',
                bg: '#d1e7dd',
                color: '#0f5132',
            },
            NOT_DELIVERYIN: {
                text: 'Not Delivery',
                bg: '#E3F2FD',
                color: '#1565C0',
            },
            AUTHORIZATION_NEEDED: {
                text: 'Authorization Needed',
                bg: '#ffeaa7',
                color: '#997404',
            },
            EXCLUDED: { text: 'Excluded', bg: '#FFF3E0', color: '#E65100' },
            UNAVAILABLE: {
                text: 'Unavailable',
                bg: '#F5F5F5',
                color: '#616161',
            },
            REJECTED: { text: 'Rejected', bg: '#FFEBEE', color: '#C62828' },
        };

        function numberToStatusCode(n) {
            switch (+n) {
                case 0:
                    return 'IN_QUEUE';
                case 1:
                    return 'LEARNING';
                case 2:
                    return 'DELIVERING';
                case 3:
                    return 'NOT_DELIVERYIN';
                case 4:
                    return 'AUTHORIZATION_NEEDED';
                case 5:
                    return 'EXCLUDED';
                case 6:
                    return 'UNAVAILABLE';
                case 7:
                    return 'REJECTED';
                default:
                    return String(n);
            }
        }

        let code;
        if (!isNaN(+value)) {
            code = numberToStatusCode(+value);
        } else {
            const upper = String(value).trim().toUpperCase();
            code = STATUS_CONFIG.hasOwnProperty(upper) ? upper : null;
        }

        if (code && STATUS_CONFIG[code]) return STATUS_CONFIG[code];

        // Fallback for numeric values
        const num = +value;
        if (
            !isNaN(num) &&
            STATUS_CONFIG.hasOwnProperty(numberToStatusCode(num))
        ) {
            return STATUS_CONFIG[numberToStatusCode(num)];
        }

        return { text: String(value), bg: '#F3F4F6', color: '#111827' };
    }

    const meta = getCreativeStatusMeta(status) || {
        text: 'Unknown',
        bg: '#F3F4F6',
        color: '#111827',
    };
    const bg = String(meta.bg || '#F3F4F6');
    const color = String(meta.color || '#111827');
    const text = String(meta.text || 'Unknown');

    const style = `display:inline-block;padding:6px 12px;border-radius:12px;font-size:12px;font-weight:600;background:${bg};color:${color};`;
    return `<span style="${style}">${text}</span>`;
};
