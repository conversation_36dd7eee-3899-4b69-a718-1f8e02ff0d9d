using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Math.EC.Rfc7748;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTok.DataSync.Services;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo GMV Max Product Campaign
    /// </summary>
    public class GmvMaxProductCampaignSyncService : BaseSyncService, IGmvMaxProductCampaignSyncService
    {
        private readonly IRawGmvMaxProductCampaignReportRepository _gmvMaxProductCampaignReportRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly ITikTokApiClientService _tikTokApiClientService;
        private readonly IApiMetricsService _apiMetricsService;
        private const string SERVICE_NAME = "GmvMaxProductCampaignSyncService";

        public GmvMaxProductCampaignSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxProductCampaignReportRepository gmvMaxProductCampaignReportRepository,
            ILogger<GmvMaxProductCampaignSyncService> logger,
            IAssetRepository assetRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ITikTokApiClientService tikTokApiClientService,
            IApiMetricsService apiMetricsService) : base(serviceProvider, logger)
        {
            _gmvMaxProductCampaignReportRepository = gmvMaxProductCampaignReportRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _assetRepository = assetRepository;
            _tikTokApiClientService = tikTokApiClientService;
            _apiMetricsService = apiMetricsService;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Product Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductCampaignSyncResult> SyncAllGmvMaxProductCampaignForAllBcsAsync()
        {
            // Bắt đầu tracking metrics
            _apiMetricsService.StartTracking(SERVICE_NAME);

            var result = new GmvMaxProductCampaignSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo GMV Max Product Campaign cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new GmvMaxProductCampaignSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncGmvMaxProductCampaignAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.CampaignCount += bcResult.CampaignCount;
                    totalResult.StoreCount += bcResult.StoreCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo GMV Max Product Campaign cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, Campaign: {CampaignCount}, Store: {StoreCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.CampaignCount, result.StoreCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo GMV Max Product Campaign cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo GMV Max Product Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo GMV Max Product Campaign cho tất cả BC");
            }
            finally
            {
                // Luôn lấy metrics trước khi return, kể cả khi có exception
                result.ApiMetrics = _apiMetricsService.GetMetrics(SERVICE_NAME);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Product Campaign theo BC ID và khoảng thời gian
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductCampaignSyncResult> SyncGmvMaxProductCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            // Bắt đầu tracking metrics nếu chưa có
            _apiMetricsService.StartTracking(SERVICE_NAME);

            var result = new GmvMaxProductCampaignSyncResult
            {
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo GMV Max Product Campaign cho BC: {BcId}", bcId);
                // Tạo TikTok client từ Singleton
                var tikTokClient = await _tikTokApiClientService.GetOrCreateClientAsync(bcId);

                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }

                var advertisers = await _assetRepository.GetByBcIdAsync(bcId,assetType:Enums.AssetType.ADVERTISER);
                var advertiserIds = advertisers.Select(x=>x.AssetId).ToList();
                // Duyệt từng nhà quảng cáo
                foreach (var advertiserId in advertiserIds)
                {
                    (DateTime startDate, DateTime endDate) rangeDateFiltering;
                    if (startDate.HasValue && endDate.HasValue)
                    {
                        if(startDate.Value > endDate.Value)
                        {
                            throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                        }
                        rangeDateFiltering = (startDate.Value, endDate.Value);
                    }
                    else
                    {

                        rangeDateFiltering = await GetRangeDateFiltering(bcId, advertiserId, bcTimezone);
                    }

                    var currentDate = rangeDateFiltering.startDate.Date;
                    var end = rangeDateFiltering.endDate.Date;

                    while (currentDate <= end)
                    {
                        var pageEnd = currentDate.AddDays(30);
                        var daysDifference = (end - currentDate).TotalDays;
                        if (daysDifference < 30)
                        {
                            pageEnd = end;
                        }
                        _logger.LogDebug("Đồng bộ dữ liệu báo cáo GMV Max Product Campaign cho ngày: {Date}", currentDate.ToString("yyyy-MM-dd"));
                        var apiResponse = await GetSyncGmvMaxProductCampaignFromApiAsync(tikTokClient, bcId, advertiserId, currentDate, pageEnd);
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, advertiserId, bcTimezone, apiResponse, result);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo GMV Max Product Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));
                        }
                        if (pageEnd == end)
                        {
                            break;
                        }
                        currentDate = currentDate.AddDays(29);

                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo GMV Max Product Campaign cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Campaign: {CampaignCount}, Store: {StoreCount}, Ngày: {DayCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.CampaignCount, result.StoreCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo GMV Max Product Campaign cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo GMV Max Product Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo GMV Max Product Campaign cho BC: {BcId}", bcId);
            }
            finally
            {
                // Luôn lấy metrics trước khi return, kể cả khi có exception
                result.ApiMetrics = _apiMetricsService.GetMetrics(SERVICE_NAME);
            }

            return result;
        }

        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId, string advertiserId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            // Lấy dữ liệu báo cáo GMV Max Product Campaign mới nhất (theo giờ)
            //var latestReport = await GetLatestReportByBcIdAsync(bcId, advertiserId);

            DateTime startDate= currentDateInTimezone;
            //if (latestReport == null)
            //{
            //    // Nếu chưa có dữ liệu trong DB thì lấy khoảng 1 tuần từ ngày hiện tại
            //    startDate = currentDateInTimezone.AddDays(-LAST_SYNC_DAYS);
            //    _logger.LogDebug("Chưa có dữ liệu báo cáo GMV Max Product Campaign trong DB cho BC: {BcId}. Lấy dữ liệu 1 tuần từ {StartDate} đến {EndDate}",
            //        bcId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            //}
            //else
            //{
            //    // Nếu có dữ liệu trong DB thì lấy từ ngày trong DB đến ngày hiện tại
            //    // Convert từ UTC (trong DB) sang timezone của BC để so sánh
            //    var latestReportDateInTimezone = _dateTimeService.ConvertFromUtc(latestReport.Date, timezone).Date;
            //    startDate = latestReportDateInTimezone;
            //    _logger.LogDebug("Có dữ liệu báo cáo GMV Max Product Campaign trong DB cho BC: {BcId}. Lấy từ {StartDate} đến {EndDate}",
            //        bcId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            //}

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID và advertiserId
        /// </summary>
        /// <param name="bcId">BC ID</param>
        /// <returns>Báo cáo mới nhất</returns>
        private async Task<RawGmvMaxProductCampaignReportEntity?> GetLatestReportByBcIdAsync(string bcId, string advertiserId)
        {
            var query = await _gmvMaxProductCampaignReportRepository.GetLatestByBcIAndAdvertiserIddAsync(bcId, advertiserId);

            return query;
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo GMV Max Product Campaign từ TikTok API
        /// Lưu ý: Khi lấy báo cáo theo giờ, startDate và endDate phải cùng một ngày
        /// </summary>
        private async Task<List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)>> GetSyncGmvMaxProductCampaignFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, string advertiserId, DateTime startDate, DateTime endDate)
        {

            var records = new List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)>();

            // Lấy danh sách StoreId từ GMV Max Campaigns theo AdvertiserId
            var gmvMaxCampaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAsync(advertiserId);
            var storeIds = gmvMaxCampaigns.Where(x => !string.IsNullOrEmpty(x.StoreId))
                                         .Select(x => x.StoreId)
                                         .Distinct()
                                         .ToList();

            if (!storeIds.Any())
            {
                _logger.LogDebug("Không có Store ID nào cho Advertiser: {AdvertiserId}", advertiserId);
                return records;
            }

            _logger.LogDebug("Tìm thấy {StoreCount} Store ID cho Advertiser: {AdvertiserId}", storeIds.Count, advertiserId);

            // Duyệt từng StoreId vì API chỉ hỗ trợ 1 StoreId mỗi lần gọi
            foreach (var storeId in storeIds)
            {
                var pageRecords = await GetReportFromApiByAdvertiserAndStoreAsync(tikTokClient, advertiserId, storeId, startDate, endDate);
                records.AddRange(pageRecords);
            }

            return records;
        }

        /// <summary>
        /// Lấy báo cáo từ API theo advertiser và store (Campaign report-advertiser)
        /// </summary>
        private async Task<List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)>> GetReportFromApiByAdvertiserAndStoreAsync(TikTokBusinessApiClient tikTokClient, string advertiserId, string storeId, DateTime startDate, DateTime endDate)
        {
            var records = new List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)>();
            var page = 1;
            const int pageSize = PAGE_SIZE_SYNC_REPORT;

            while (true)
            {
                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = new List<string> { storeId },
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Dimensions = new List<string> { "campaign_id", "stat_time_day" },
                    Metrics = new List<string>
                    {
                        "currency", "campaign_id", "operation_status", "campaign_name",
                        "schedule_type", "schedule_start_time", "schedule_end_time",
                        "target_roi_budget", "bid_type", "max_delivery_budget", "roas_bid",
                        "cost", "net_cost", "orders", "cost_per_order", "gross_revenue", "roi"
                    },
                    Filtering = new GMVMaxReportFiltering
                    {
                        GMVMaxPromotionTypes = new List<string> { "PRODUCT" }
                    },
                    Page = page,
                    PageSize = pageSize
                };

                // Ghi nhận API call và kiểm tra giới hạn
                if (!_apiMetricsService.RecordApiCall(SERVICE_NAME))
                {
                    throw new BusinessException($"Đã vượt giới hạn API calls cho service {SERVICE_NAME} trong môi trường Development");
                }
                var response = await tikTokClient.GMVMax.GetReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo GMV Max Product Campaign: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                // Thêm AdvertiserId và StoreId vào mỗi item
                foreach (var item in response.Data.List)
                {
                    records.Add((item, advertiserId, storeId));
                }

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, string advertiserId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)> reportDataList, GmvMaxProductCampaignSyncResult result)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 200 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo GMV Max Product Campaign để xử lý cho {Total} bản ghi", reportDataList?.Count ?? 0);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo GMV Max Product Campaign cho {TotalPages} trang", reportDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, bcTimezone, pageData, result);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo GMV Max Product Campaign cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)> pageData, GmvMaxProductCampaignSyncResult result)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, pageData);

                var existingEntities = await GetExistingEntitiesAsync(bcId, mappedEntities);

                var insertedEntities = new List<RawGmvMaxProductCampaignReportEntity>();
                var updatedEntities = new List<RawGmvMaxProductCampaignReportEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                    // So sánh theo CampaignId và Date với độ chính xác đến giờ
                    var currentEntity = existingEntities.FirstOrDefault(x => x.CampaignId == mappedEntity.CampaignId && x.Date == mappedEntity.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                        result.TotalSynced++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (currentEntity.HasChanged(mappedEntity))
                        {
                            currentEntity.UpdateFrom(mappedEntity);
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _gmvMaxProductCampaignReportRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _gmvMaxProductCampaignReportRepository.UpdateManyAsync(updatedEntities);
                }

                result.DayCount += pageData.Count;
                result.CampaignCount += pageData.Select(x => x.reportItem.Dimensions?.GetValueOrDefault("campaign_id")?.ToString()).Distinct().Count();
                result.StoreCount += pageData.Select(x => x.storeId).Distinct().Count();

                await uow.CompleteAsync();
            }
        }

        /// <summary>
        /// Lấy các entity hiện có trong database
        /// </summary>
        private async Task<List<RawGmvMaxProductCampaignReportEntity>> GetExistingEntitiesAsync(string bcId, List<RawGmvMaxProductCampaignReportEntity> mappedEntities)
        {
            if (!mappedEntities.Any())
                return new List<RawGmvMaxProductCampaignReportEntity>();

            var campaignIds = mappedEntities.Select(x => x.CampaignId).Distinct().ToList();
            var storeIds = mappedEntities.Select(x => x.StoreId).Distinct().ToList();
            var advertiserIds = mappedEntities.Select(x => x.AdvertiserId).Distinct().ToList();

            var minDate = mappedEntities.Min(x => x.Date);
            var maxDate = mappedEntities.Max(x => x.Date);

            var query = await _gmvMaxProductCampaignReportRepository.GetQueryableAsync();
            return query.Where(x => x.BcId == bcId &&
                                      campaignIds.Contains(x.CampaignId) &&
                                      storeIds.Contains(x.StoreId) &&
                                      advertiserIds.Contains(x.AdvertiserId) &&
                                        x.Date >= minDate && x.Date <= maxDate).ToList();
        }

        private async Task<List<RawGmvMaxProductCampaignReportEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId)> reportDataList)
        {
            var entities = new List<RawGmvMaxProductCampaignReportEntity>();
            foreach (var (reportItem, advertiserId, storeId) in reportDataList)
            {
                var entity = MapReportDataToEntity(bcId, bcTimezone, reportItem, advertiserId, storeId);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return entities;
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="storeId">ID của Store</param>
        /// <returns>RawGmvMaxProductCampaignReportEntity</returns>
        private RawGmvMaxProductCampaignReportEntity? MapReportDataToEntity(string bcId, string bcTimezone, GMVMaxReportItem reportData, string advertiserId, string storeId)
        {
            var dateTimeStr = reportData.Dimensions?.GetValueOrDefault("stat_time_day")?.ToString();

            if (string.IsNullOrEmpty(dateTimeStr) || !DateTime.TryParse(dateTimeStr, out var reportDateTime))
            {
                _logger.LogWarning("Ngày giờ báo cáo GMV Max Product Campaign không hợp lệ: {DateTime} cho BC: {BcId}", dateTimeStr, bcId);
                return null;
            }

            // Convert datetime từ timezone của campaign sang UTC
            var reportDateTimeUtc = _dateTimeService.ConvertToUtc(reportDateTime, bcTimezone);

            var campaignId = reportData.Dimensions?.GetValueOrDefault("campaign_id")?.ToString();
            if (string.IsNullOrEmpty(campaignId))
            {
                _logger.LogWarning("Campaign ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            var entity = new RawGmvMaxProductCampaignReportEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                StoreId = storeId,
                CampaignId = campaignId,
                Date = reportDateTimeUtc  // Lưu datetime UTC với giờ
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.CampaignName = GetStringValue(reportData.Metrics, "campaign_name");
                entity.OperationStatus = GetStringValue(reportData.Metrics, "operation_status");
                entity.ScheduleType = GetStringValue(reportData.Metrics, "schedule_type");

                // Map schedule times
                var scheduleStartTimeStr = GetStringValue(reportData.Metrics, "schedule_start_time");
                if (!string.IsNullOrEmpty(scheduleStartTimeStr) && DateTime.TryParse(scheduleStartTimeStr, out var scheduleStartTime))
                {
                    entity.ScheduleStartTime = scheduleStartTime;
                }

                var scheduleEndTimeStr = GetStringValue(reportData.Metrics, "schedule_end_time");
                if (!string.IsNullOrEmpty(scheduleEndTimeStr) && DateTime.TryParse(scheduleEndTimeStr, out var scheduleEndTime))
                {
                    entity.ScheduleEndTime = scheduleEndTime;
                }

                entity.BidType = GetStringValue(reportData.Metrics, "bid_type");
                entity.TargetRoiBudget = GetDecimalValue(reportData.Metrics, "target_roi_budget");
                entity.MaxDeliveryBudget = GetDecimalValue(reportData.Metrics, "max_delivery_budget");
                entity.RoasBid = GetDecimalValue(reportData.Metrics, "roas_bid");
                entity.Cost = GetDecimalValue(reportData.Metrics, "cost");
                entity.NetCost = GetDecimalValue(reportData.Metrics, "net_cost");
                entity.Orders = GetIntValue(reportData.Metrics, "orders");
                entity.CostPerOrder = GetDecimalValue(reportData.Metrics, "cost_per_order");
                entity.GrossRevenue = GetDecimalValue(reportData.Metrics, "gross_revenue");
                entity.ROI = GetDecimalValue(reportData.Metrics, "roi");
                entity.Currency = GetStringValue(reportData.Metrics, "currency") ?? "USD";
            }

            return entity;
        }
    }
}