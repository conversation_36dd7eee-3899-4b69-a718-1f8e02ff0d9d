<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>TikTok</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\TikTok.Domain.Shared\TikTok.Domain.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Volo.Abp.Emailing" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Identity.Domain" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.BackgroundJobs.Domain" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.AuditLogging.Domain" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.Domain" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.FeatureManagement.Domain" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Domain" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.OpenIddict.Domain" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="8.1.4" />
		<PackageReference Include="Tsp.Zalo.Domain" Version="1.0.0-prerelease-5916" />
    <PackageReference Include="Tsp.Module.Notifications.Domain" Version="1.0.17" />
		<PackageReference Include="Dapper" Version="2.1.35" />
		<PackageReference Include="Dapper.Contrib" Version="2.0.78" />
	</ItemGroup>

</Project>
