/**
 * GMV Max Analysis Main Controller
 * Manages the overall dashboard functionality with Campaign and Video tabs
 */

class GmvMaxDashboard {
    constructor() {
        this.config = null;
        this.permissions = null;
        this.campaignTab = null;
        this.videoTab = null;
        this.dataAggregator = null;
        this.autoRefreshInterval = null;

        this.init();
    }

    /**
     * Initialize the dashboard
     */
    async init() {
        try {
            // Load configuration from DOM
            this.loadConfiguration();

            // Parse URL parameters for notification redirects
            const urlParams = this.parseUrlParameters();

            // Initialize permissions
            this.initializePermissions();

            // Initialize data aggregator
            this.dataAggregator = new GmvMaxDataAggregator(this.config);

            // Initialize tab managers
            await this.initializeTabManagers();

            // Setup event listeners
            this.setupEventListeners();

            // Handle URL parameters (auto-switch tab and apply filters)
            await this.handleUrlParameters(urlParams);

            // Load initial data
            await this.loadInitialData();
        } catch (error) {
            console.error('❌ Error initializing GMV Max Analysis:', error);
            this.showErrorToast('Lỗi khởi tạo dashboard: ' + error.message);
        }
    }

    /**
     * Parse URL parameters for notification redirects and filters
     */
    parseUrlParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
            campaignId: urlParams.get('campaignId'),
            tab: urlParams.get('tab'),
            // Add more parameters as needed
        };
    }

    /**
     * Handle URL parameters (auto-switch tab and apply filters)
     */
    async handleUrlParameters(urlParams) {
        try {
            // Auto-switch to video tab if specified
            if (urlParams.tab === 'video' && this.videoTab) {
                await this.switchToVideoTab();
            }

            // Auto-apply campaign filter if campaignId is provided
            if (urlParams.campaignId && this.videoTab) {
                await this.applyNotificationCampaignFilter(
                    urlParams.campaignId
                );
            }

            // Clear URL parameters after processing to avoid confusion
            if (urlParams.campaignId || urlParams.tab) {
                this.clearUrlParameters();
            }
        } catch (error) {
            console.error('❌ Error handling URL parameters:', error);
        }
    }

    /**
     * Load configuration from DOM data attributes
     */
    loadConfiguration() {
        const configElement = document.getElementById('gmv-max-config');
        if (!configElement) {
            throw new Error('Configuration element not found');
        }

        this.config = {
            apiEndpoints: {
                campaign: configElement.dataset.apiCampaignEndpoint,
                product: configElement.dataset.apiProductEndpoint,
                creative: configElement.dataset.apiCreativeEndpoint,
            },
            refreshInterval:
                parseInt(configElement.dataset.refreshInterval) || 300000,
            locale: configElement.dataset.locale || 'vi-VN',
            currency: configElement.dataset.currency || 'USD',
            dateRange: {
                from: configElement.dataset.fromDate,
                to: configElement.dataset.toDate,
            },
            autoRefresh: configElement.dataset.autoRefresh === 'true',
        };
    }

    /**
     * Initialize permissions from existing permission helper
     */
    initializePermissions() {
        // Use existing permissionHelper if available
        if (typeof window.permissionHelper !== 'undefined') {
            this.permissions = window.permissionHelper;
        } else {
            // Fallback permissions based on UI visibility
            this.permissions = {
                hasViewSpending:
                    document.getElementById('campaign-content') !== null,
                hasViewMetrics:
                    document.getElementById('campaign-content') !== null,
                hasViewAll:
                    document.getElementById('campaign-content') !== null,
                hasViewVideoTab:
                    document.getElementById('video-content') !== null,
            };
        }
    }

    /**
     * Initialize tab managers
     */
    async initializeTabManagers() {
        // Initialize Campaign Tab if user has permissions
        if (document.getElementById('campaign-content')) {
            this.campaignTab = new CampaignTabManager(
                this.config,
                this.dataAggregator
            );
            await this.campaignTab.init();

            // ✅ Make campaignTab globally accessible for ROI modal
            window.campaignTabManager = this.campaignTab;
        }

        // Initialize Video Tab if user has permissions
        if (document.getElementById('video-content')) {
            this.videoTab = new VideoTabManager(
                this.config,
                this.dataAggregator
            );
            await this.videoTab.init();
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Tab switching events
        const tabButtons = document.querySelectorAll(
            '#gmvMaxTabs button[data-bs-toggle="tab"]'
        );
        tabButtons.forEach((button) => {
            button.addEventListener('shown.bs.tab', (event) => {
                this.handleTabSwitch(
                    event.target.getAttribute('aria-controls')
                );
            });
        });

        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById(
            'auto-refresh-toggle'
        );
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('click', () => {
                this.toggleAutoRefresh();
            });
        }

        // Window resize handler for responsive pivot tables
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
    }

    /**
     * Load initial data for active tab
     */
    async loadInitialData() {
        try {
            // Get active tab
            const activeTab = document.querySelector(
                '#gmvMaxTabs .nav-link.active'
            );
            if (!activeTab) return;

            const activeTabId = activeTab.getAttribute('aria-controls');
            await this.loadTabData(activeTabId);
        } catch (error) {
            console.error('❌ Error loading initial data:', error);
            this.showErrorToast('Lỗi tải dữ liệu ban đầu: ' + error.message);
        }
    }

    /**
     * Handle tab switch
     */
    async handleTabSwitch(tabId) {
        try {
            await this.loadTabData(tabId);
            this.updateLastUpdatedTime();
        } catch (error) {
            console.error(`❌ Error switching to tab ${tabId}:`, error);
            this.showErrorToast('Lỗi chuyển tab: ' + error.message);
        }
    }

    /**
     * Load data for specific tab
     */
    async loadTabData(tabId) {
        switch (tabId) {
            case 'campaign-content':
                if (this.campaignTab) {
                    await this.campaignTab.loadData();
                }
                break;
            case 'video-content':
                if (this.videoTab) {
                    await this.videoTab.loadData();
                }
                break;
            default:
                console.warn(`Unknown tab ID: ${tabId}`);
        }
    }

    /**
     * Toggle auto-refresh functionality
     */
    toggleAutoRefresh() {
        const statusElement = document.getElementById('auto-refresh-status');

        if (this.autoRefreshInterval) {
            // Stop auto-refresh
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
            statusElement.textContent = 'OFF';
            this.showInfoToast('Auto-refresh đã tắt');
        } else {
            // Start auto-refresh
            this.autoRefreshInterval = setInterval(() => {
                this.refreshCurrentTab();
            }, this.config.refreshInterval);
            statusElement.textContent = 'ON';
            this.showInfoToast(
                `Auto-refresh đã bật (${this.config.refreshInterval / 1000}s)`
            );
        }
    }

    /**
     * Refresh current active tab
     */
    async refreshCurrentTab() {
        try {
            const activeTab = document.querySelector(
                '#gmvMaxTabs .nav-link.active'
            );
            if (!activeTab) return;

            const activeTabId = activeTab.getAttribute('aria-controls');

            await this.loadTabData(activeTabId);
            this.updateLastUpdatedTime();
        } catch (error) {
            console.error('❌ Error during auto-refresh:', error);
        }
    }

    /**
     * Handle window resize
     */
    handleWindowResize() {
        // Notify tab managers about resize
        if (this.campaignTab && this.campaignTab.handleResize) {
            this.campaignTab.handleResize();
        }
        if (this.videoTab && this.videoTab.handleResize) {
            this.videoTab.handleResize();
        }
    }

    /**
     * Update last updated time
     */
    updateLastUpdatedTime() {
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            const now = new Date();
            lastUpdatedElement.textContent = now.toLocaleString('vi-VN');
        }
    }

    /**
     * Switch to video tab programmatically
     */
    async switchToVideoTab() {
        try {
            const videoTabButton = document.getElementById('video-tab');
            if (videoTabButton) {
                // Trigger Bootstrap tab switch
                const tabTrigger = new bootstrap.Tab(videoTabButton);
                tabTrigger.show();

                console.log('✅ Switched to Video tab from notification');
            }
        } catch (error) {
            console.error('❌ Error switching to video tab:', error);
        }
    }

    /**
     * Apply campaign filter from notification
     */
    async applyNotificationCampaignFilter(campaignId) {
        try {
            if (!this.videoTab || !campaignId) return;

            // Use the new setCampaignFilter method
            await this.videoTab.setCampaignFilter(campaignId);

            console.log(
                `✅ Applied campaign filter: ${campaignId} from notification`
            );
        } catch (error) {
            console.error(
                '❌ Error applying notification campaign filter:',
                error
            );
        }
    }

    /**
     * Clear URL parameters after processing
     */
    clearUrlParameters() {
        try {
            const url = new URL(window.location);
            url.searchParams.delete('campaignId');
            url.searchParams.delete('tab');
            window.history.replaceState(
                {},
                document.title,
                url.pathname + url.search
            );
        } catch (error) {
            console.error('❌ Error clearing URL parameters:', error);
        }
    }

    /**
     * Show success toast
     */
    showSuccessToast(message) {
        this.showToast('success', message);
    }

    /**
     * Show error toast
     */
    showErrorToast(message) {
        this.showToast('error', message);
    }

    /**
     * Show info toast
     */
    showInfoToast(message) {
        this.showToast('info', message);
    }

    /**
     * Show toast notification
     */
    showToast(type, message) {
        const toastElement = document.getElementById(`${type}-toast`);
        const messageElement = document.getElementById(`${type}-message`);

        if (toastElement && messageElement) {
            messageElement.textContent = message;
            const toast = new bootstrap.Toast(toastElement);
            toast.show();
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }

        if (this.campaignTab && this.campaignTab.destroy) {
            this.campaignTab.destroy();
        }

        if (this.videoTab && this.videoTab.destroy) {
            this.videoTab.destroy();
        }
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
    // Check if we're on the GMV Max page
    if (document.getElementById('gmv-max-config')) {
        window.gmvMaxDashboard = new GmvMaxDashboard();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function () {
    if (window.gmvMaxDashboard) {
        window.gmvMaxDashboard.destroy();
    }
});
