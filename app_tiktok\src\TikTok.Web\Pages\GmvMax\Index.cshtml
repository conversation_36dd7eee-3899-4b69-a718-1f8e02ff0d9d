@page "~/GmvMax"
@model TikTok.Web.Pages.GmvMax.IndexModel
@{
    ViewData["Title"] = "GMV Max Analysis - Phân tích GMV Max";
}

@section Styles {
    <!-- ✅ Optimized: Preload critical CSS -->
    <link rel="preload" href="https://cdn.syncfusion.com/ej2/material.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://cdn.syncfusion.com/ej2/material.css" rel="stylesheet"></noscript>
    
    <link href="/css/dashboard-common.css" rel="stylesheet" />
    <link href="/css/gmv-max.css" rel="stylesheet" />
    <link href="/css/toast.css" rel="stylesheet" />
}

@section Scripts {
    <script src="https://cdn.syncfusion.com/ej2/locale/vi.js" defer></script>
    <!-- Syncfusion Localization -->
    <script src="/js/syncfusion_localization.js"></script>
    <!-- ✅ Load Syncfusion immediately for pivot table -->
    <script src="https://cdn.syncfusion.com/ej2/dist/ej2.min.js"></script>

    <!-- Custom Scripts -->
    <script src="/js/shared/common-utils.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/shared/date-helper.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/shared/currency-manager.js?v=@DateTime.Now.Ticks"></script>
    <!-- ✅ Load permissionHelper.js FIRST before currency.js -->
    <script src="/js/permissionHelper.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/currency.js?v=@DateTime.Now.Ticks"></script>
    
    <!-- Reuse existing utility scripts -->
    <script src="/js/factGmvMaxProducts/utils/helper.js?v=@DateTime.Now.Ticks" defer></script>
    <script src="/js/factGmvMaxProducts/utils/businessHelper.js?v=@DateTime.Now.Ticks" defer></script>
    <script src="/js/factGmvMaxProducts/pivot/conditionalFormatting.js?v=@DateTime.Now.Ticks" defer></script>
    <script src="/js/factGmvMaxCampaign/utils/helper.js?v=@DateTime.Now.Ticks" defer></script>
    <script src="/js/factGmvMaxCampaign/utils/businessHelper.js?v=@DateTime.Now.Ticks" defer></script>
    <script src="/js/factGmvMaxCampaign/pivot/conditionalFormatting.js?v=@DateTime.Now.Ticks" defer></script>
    
    <!-- GMV Max Scripts -->
    <script src="/js/gmvMax/dataAggregator.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/gmvMax/components/roiCards.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/gmvMax/components/videoStatusCards.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/gmvMax/components/productPivotTable.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/gmvMax/components/campaignPivotTable.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/gmvMax/components/valueSelectionModals.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/gmvMax/campaignTab.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/gmvMax/videoTab.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/gmvMax/gmvMax.js?v=@DateTime.Now.Ticks"></script>
}

<div id="gmv-max-config"
     data-api-campaign-endpoint="@Url.Action("GetGmvMaxCampaignData", "FactGmvMaxCampaign")"
     data-api-product-endpoint="@Url.Action("GetGmvMaxProductData", "FactGmvMaxProduct")"
     data-api-creative-endpoint="/api/raw-gmv-max-product-creative-report"
     data-refresh-interval="300000"
     data-locale="vi-VN"
     data-currency="@(Model.Currency ?? "USD")"
     data-from-date="@((Model.FromDate ?? Model.From ?? DateTime.Now.AddDays(-7)).ToString("yyyy-MM-dd"))"
     data-to-date="@((Model.ToDate ?? Model.To ?? DateTime.Now).ToString("yyyy-MM-dd"))"
     data-auto-refresh="false"></div>

<div class="container-fluid">
    <!-- ✅ Main Tab Navigation -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3><i class="bi bi-tiktok"></i> Phân tích GMV Max</h3>
                    <ul class="nav nav-tabs card-header-tabs" id="gmvMaxTabs" role="tablist">
                        <!-- Campaign Tab -->
                        @if (Model.HasViewSpending || Model.HasViewMetrics || Model.HasViewAll || Model.HasViewAllAdvertisers)
                        {
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="campaign-tab" data-bs-toggle="tab" data-bs-target="#campaign-content" type="button" role="tab" aria-controls="campaign-content" aria-selected="true">
                                    <i class="fas fa-bullhorn"></i> Campaign
                                </button>
                            </li>
                        }
                        
                        <!-- Video Tab -->
                        @if (Model.HasViewVideoTab)
                        {
                            <li class="nav-item" role="presentation">
                                <button class="nav-link @(Model.HasViewSpending || Model.HasViewMetrics || Model.HasViewAll || Model.HasViewAllAdvertisers ? "" : "active")" id="video-tab" data-bs-toggle="tab" data-bs-target="#video-content" type="button" role="tab" aria-controls="video-content" aria-selected="@(Model.HasViewSpending || Model.HasViewMetrics || Model.HasViewAll || Model.HasViewAllAdvertisers ? "false" : "true")">
                                    <i class="fas fa-video"></i> Video
                                </button>
                            </li>
                        }
                    </ul>
                </div>
                
                <div class="card-body p-0">
                    <!-- Tab Content -->
                    <div class="tab-content" id="gmvMaxTabContent">
                        <!-- Campaign Tab Content -->
                        @if (Model.HasViewSpending || Model.HasViewMetrics || Model.HasViewAll || Model.HasViewAllAdvertisers)
                        {
                            <div class="tab-pane fade show active" id="campaign-content" role="tabpanel" aria-labelledby="campaign-tab">
                                <div class="px-4">
                                    <!-- Campaign Tab Filters -->
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="card filter-section">
                                                <div class="card-body">
                                                    <div class="row">
                                                        <!-- Text Search -->
                                                        <div class="col-md-4">
                                                            <label class="form-label fw-bold"><i class="fas fa-search"></i> Tìm kiếm:</label>
                                                            <input type="text" class="form-control form-control-sm" 
                                                                   id="campaign-keyword-search" 
                                                                   placeholder="Tìm kiếm theo BCID, shop ID, campaign ID">
                                                        </div>
                                                        
                                                        <!-- Shop Filter -->
                                                        <div class="col-md-3">
                                                            <label class="form-label fw-bold"><i class="fas fa-store"></i> Shop:</label>
                                                            <input type="text" id="campaign-shop-multiselect" />
                                                        </div>
                                                        
                                                        <!-- Date Range Filter -->
                                                        <div class="col-md-3">
                                                            <label class="form-label fw-bold"><i class="fas fa-calendar-alt"></i> Thời gian:</label>
                                                            <input type="text" id="campaign-date-range-picker" />
                                                        </div>
                                                        
                                                        <!-- Quick Date Filter -->
                                                        <div class="col-md-2">
                                                            <label class="form-label fw-bold"><i class="fas fa-clock"></i> Nhanh:</label>
                                                            <input type="text" id="campaign-quick-date-dropdown" />
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="filter-actions">
                                                        <div class="row">
                                                            <div class="col-12 text-end">
                                                                <button class="btn btn-outline-secondary btn-sm me-2" id="clear-campaign-filters">
                                                                    <i class="fas fa-times"></i> Xóa bộ lọc
                                                                </button>
                                                                <button class="btn btn-success btn-sm" id="apply-campaign-filters">
                                                                    <i class="fas fa-check"></i> Áp dụng
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- ROI Analysis moved to individual sections -->
                                    
                                    <!-- Campaign Data Sections -->
                                    <div class="row">
                                        <div class="col-12">
                                            <!-- Product Campaign Section -->
                                            <div class="card mb-4">
                                                <div class="card-header" data-bs-toggle="collapse" data-bs-target="#productCampaignCollapse" aria-expanded="true">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <h5 class="mb-0"><i class="fas fa-shopping-bag"></i> PRODUCT</h5>
                                                        <i class="fas fa-chevron-down"></i>
                                                    </div>
                                                </div>
                                                <div class="collapse show" id="productCampaignCollapse">
                                                    <div class="card-body p-2">
                                                        <!-- Product ROI Analysis Cards -->
                                                        <div class="row mb-4" id="product-roi-cards-container">
                                                            <!-- Loading state -->
                                                            <div class="col-12 text-center p-4">
                                                                <div class="spinner-border text-success" role="status">
                                                                    <span class="visually-hidden">Loading...</span>
                                                                </div>
                                                                <p class="mt-2 text-muted">Đang tải phân tích ROI Product...</p>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- Main Actions -->
                                                        <div class="mb-3">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <div class="btn-group me-2 mb-2" role="group">
                                                                        <button type="button" class="btn btn-primary btn-sm" id="refresh-product-data">
                                                                            <i class="fas fa-sync-alt"></i> Làm mới
                                                                        </button>
                                                                        <button type="button" class="btn btn-info btn-sm" id="toggle-product-chart">
                                                                            <i class="fas fa-chart-bar"></i> Biểu đồ
                                                                        </button>
                                                                        <button type="button" class="btn btn-outline-warning btn-sm" id="open-product-value-selection-modal">
                                                                            <i class="fas fa-sliders-h"></i> Cấu hình cột & Chỉ số
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 d-flex justify-content-end">
                                                                    <div class="btn-group me-2 mb-2" role="group">
                                                                        <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                                            <i class="fas fa-download"></i> Xuất báo cáo
                                                                        </button>
                                                                        <ul class="dropdown-menu">
                                                                            <li><a class="dropdown-item" href="#" id="export-product-excel"><i class="fas fa-file-excel"></i> Excel (.xlsx)</a></li>
                                                                            <li><a class="dropdown-item" href="#" id="export-product-pdf"><i class="fas fa-file-pdf"></i> PDF Report</a></li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- Loading indicator -->
                                                        <div id="product-pivot-loading" class="text-center p-4">
                                                            <div class="spinner-border text-primary" role="status">
                                                                <span class="visually-hidden">Loading...</span>
                                                            </div>
                                                            <p class="mt-2 text-muted">Đang tải bảng phân tích Product...</p>
                                                        </div>
                                                        
                                                        <!-- Pivot table container -->
                                                        <div id="productCampaignPivotTable" style="display: none;"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Live Campaign Section -->
                                            <div class="card mb-4">
                                                <div class="card-header" data-bs-toggle="collapse" data-bs-target="#liveCampaignCollapse" aria-expanded="true">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <h5 class="mb-0"><i class="fas fa-video"></i> LIVE</h5>
                                                        <i class="fas fa-chevron-down"></i>
                                                    </div>
                                                </div>
                                                <div class="collapse show" id="liveCampaignCollapse">
                                                    <div class="card-body p-2">
                                                        <!-- Live ROI Analysis Cards -->
                                                        <div class="row mb-4" id="live-roi-cards-container">
                                                            <!-- Loading state -->
                                                            <div class="col-12 text-center p-4">
                                                                <div class="spinner-border text-warning" role="status">
                                                                    <span class="visually-hidden">Loading...</span>
                                                                </div>
                                                                <p class="mt-2 text-muted">Đang tải phân tích ROI Live...</p>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- Main Actions -->
                                                        <div class="mb-3">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <div class="btn-group me-2 mb-2" role="group">
                                                                        <button type="button" class="btn btn-primary btn-sm" id="refresh-live-data">
                                                                            <i class="fas fa-sync-alt"></i> Làm mới
                                                                        </button>
                                                                        <button type="button" class="btn btn-info btn-sm" id="toggle-live-chart">
                                                                            <i class="fas fa-chart-bar"></i> Biểu đồ
                                                                        </button>
                                                                        <button type="button" class="btn btn-outline-warning btn-sm" id="open-live-value-selection-modal">
                                                                            <i class="fas fa-sliders-h"></i> Cấu hình cột & Chỉ số
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 d-flex justify-content-end">
                                                                    <div class="btn-group me-2 mb-2" role="group">
                                                                        <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                                            <i class="fas fa-download"></i> Xuất báo cáo
                                                                        </button>
                                                                        <ul class="dropdown-menu">
                                                                            <li><a class="dropdown-item" href="#" id="export-live-excel"><i class="fas fa-file-excel"></i> Excel (.xlsx)</a></li>
                                                                            <li><a class="dropdown-item" href="#" id="export-live-pdf"><i class="fas fa-file-pdf"></i> PDF Report</a></li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- Loading indicator -->
                                                        <div id="live-pivot-loading" class="text-center p-4">
                                                            <div class="spinner-border text-primary" role="status">
                                                                <span class="visually-hidden">Loading...</span>
                                                            </div>
                                                            <p class="mt-2 text-muted">Đang tải bảng phân tích Live...</p>
                                                        </div>
                                                        
                                                        <!-- Pivot table container -->
                                                        <div id="liveCampaignPivotTable" style="display: none;"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        
                        <!-- Video Tab Content -->
                        @if (Model.HasViewVideoTab)
                        {
                            <div class="tab-pane fade @(Model.HasViewSpending || Model.HasViewMetrics || Model.HasViewAll || Model.HasViewAllAdvertisers ? "" : "show active")" id="video-content" role="tabpanel" aria-labelledby="video-tab">
                                <div class="px-4">
                                    <!-- Video Tab Filters -->
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="card filter-section">
                                                <div class="card-body">
                                                    <div class="row">
                                                        <!-- Text Search -->
                                                        <div class="col-md-4">
                                                            <label class="form-label fw-bold"><i class="fas fa-search"></i> Tìm kiếm:</label>
                                                            <input type="text" class="form-control form-control-sm"
                                                                   id="video-keyword-search"
                                                                   placeholder="Tìm kiếm theo tên video, campaign ID, tên kênh">
                                                        </div>

                                                        <!-- Campaign Filter -->
                                                        <div class="col-md-3">
                                                            <label class="form-label fw-bold"><i class="fas fa-bullhorn"></i> Campaign:</label>
                                                            <input type="text" id="video-campaign-multiselect" />
                                                        </div>

                                                        <!-- Date Range Filter -->
                                                        <div class="col-md-3">
                                                            <label class="form-label fw-bold"><i class="fas fa-calendar-alt"></i> Thời gian:</label>
                                                            <input type="text" id="video-date-range-picker" />
                                                        </div>

                                                        <!-- Quick Date Filter -->
                                                        <div class="col-md-2">
                                                            <label class="form-label fw-bold"><i class="fas fa-clock"></i> Nhanh:</label>
                                                            <input type="text" id="video-quick-date-dropdown" />
                                                        </div>
                                                    </div>

                                                    <div class="filter-actions">
                                                        <div class="row">
                                                            <div class="col-12 text-end">
                                                                <button class="btn btn-outline-secondary btn-sm me-2" id="clear-video-filters">
                                                                    <i class="fas fa-times"></i> Xóa bộ lọc
                                                                </button>
                                                                <button class="btn btn-success btn-sm" id="apply-video-filters">
                                                                    <i class="fas fa-check"></i> Áp dụng
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Video Status Cards -->
                                    <div class="row mb-4" id="video-status-cards-container">
                                        <!-- Loading state -->
                                        <div class="col-12 text-center p-4">
                                            <div class="spinner-border text-info" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 text-muted">Đang tải thống kê video...</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Problematic Videos Table -->
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-header">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="d-flex align-items-center gap-3">
                                                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Video cần xử lý</h5>
                                                            <span id="problematic-videos-count" class="badge bg-warning">0</span>
                                                        </div>
                                                        <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-problematic-videos">
                                                            <i class="fas fa-sync-alt"></i> Làm mới
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="card-body">

                                                    <!-- Syncfusion Grid for Problematic Videos -->
                                                    <div id="problematic-videos-grid"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ Enhanced Footer -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="footer-info">
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <small>
                                    <i class="fas fa-clock text-primary"></i>
                                    Dữ liệu cập nhật lần cuối: <strong id="last-updated">@DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")</strong>
                                    | <i class="fas fa-info-circle text-muted"></i>
                                    Khoảng thời gian: <strong>@((Model.FromDate ?? Model.From ?? DateTime.Now.AddDays(-7)).ToString("dd/MM/yyyy"))</strong> - <strong>@((Model.ToDate ?? Model.To ?? DateTime.Now).ToString("dd/MM/yyyy"))</strong>
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="auto-refresh-toggle">
                                        <i class="fas fa-sync-alt"></i> Auto-refresh: <span id="auto-refresh-status">OFF</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ✅ Enhanced Toast notifications -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1100">
    <div id="success-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-success text-white">
            <i class="fas fa-check-circle me-2"></i>
            <strong class="me-auto">Thành công</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="success-message">
            Thao tác đã được thực hiện thành công!
        </div>
    </div>

    <div id="error-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-danger text-white">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong class="me-auto">Lỗi</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="error-message">
            Có lỗi xảy ra trong quá trình thực hiện!
        </div>
    </div>

    <div id="info-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-info text-white">
            <i class="fas fa-info-circle me-2"></i>
            <strong class="me-auto">Thông tin</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="info-message">
            Thông tin hệ thống
        </div>
    </div>
</div>

<!-- ROI Details Modal -->
<div class="modal fade" id="roiDetailsModal" tabindex="-1" aria-labelledby="roiDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-bottom">
                <h5 class="modal-title" id="roiDetailsModalLabel">
                    <i class="fas fa-chart-line"></i> Chi tiết ROI Campaign
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div id="roi-details-content">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer border-top">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Đóng
                </button>
            </div>
        </div>
    </div>
</div>


<!-- Product Value Selection Modal -->
<div class="modal fade" id="productValueSelectionModal" tabindex="-1" aria-labelledby="productValueSelectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productValueSelectionModalLabel">
                    <i class="fas fa-sliders-h"></i> Lựa chọn giá trị hiển thị - Product
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
                <div class="modal-body">
                    <!-- Quick Selection Buttons - Permission Based -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="btn-group flex-wrap" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2 mb-2" id="select-product-default">
                                        <i class="fas fa-star"></i> Mặc định
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm me-2 mb-2" id="select-product-revenue-cost" style="display: none;">
                                        <i class="fas fa-dollar-sign"></i> Doanh thu & Chi phí
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm me-2 mb-2" id="select-product-metrics" style="display: none;">
                                        <i class="fas fa-chart-line"></i> Chỉ số
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm me-2 mb-2" id="select-all-product" style="display: none;">
                                        <i class="fas fa-check-double"></i> Tất cả
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mt-2">
                                    <span class="badge bg-primary fs-6">Đã chọn: <span id="product-selected-count">0</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                <div class="row" id="product-value-checkbox-list">
                    <!-- Checkboxes will be rendered here by JS -->
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-primary" id="apply-product-value-selection">
                            <i class="fas fa-check"></i> Áp dụng lựa chọn
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            Hủy
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Live Campaign Value Selection Modal -->
<div class="modal fade" id="liveValueSelectionModal" tabindex="-1" aria-labelledby="liveValueSelectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="liveValueSelectionModalLabel">
                    <i class="fas fa-sliders-h"></i> Lựa chọn giá trị hiển thị - Live Campaign
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
                <div class="modal-body">
                    <!-- Quick Selection Buttons - Permission Based -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="btn-group flex-wrap" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2 mb-2" id="select-live-default">
                                        <i class="fas fa-star"></i> Mặc định
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm me-2 mb-2" id="select-live-revenue-cost" style="display: none;">
                                        <i class="fas fa-dollar-sign"></i> Doanh thu & Chi phí
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm me-2 mb-2" id="select-live-metrics" style="display: none;">
                                        <i class="fas fa-chart-line"></i> Chỉ số
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm me-2 mb-2" id="select-all-live" style="display: none;">
                                        <i class="fas fa-check-double"></i> Tất cả
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mt-2">
                                    <span class="badge bg-primary fs-6">Đã chọn: <span id="live-selected-count">0</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                <div class="row" id="live-value-checkbox-list">
                    <!-- Checkboxes will be rendered here by JS -->
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-primary" id="apply-live-value-selection">
                            <i class="fas fa-check"></i> Áp dụng lựa chọn
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            Hủy
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
