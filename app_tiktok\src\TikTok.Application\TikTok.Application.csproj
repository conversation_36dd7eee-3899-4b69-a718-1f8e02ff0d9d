﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>TikTok</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\TikTok.Domain\TikTok.Domain.csproj" />
		<ProjectReference Include="..\TikTok.Application.Contracts\TikTok.Application.Contracts.csproj" />
		<ProjectReference Include="..\..\..\net_sdk\src\TikTokBusinessApi\TikTokBusinessApi.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Elsa.Core" Version="2.14.1" />
		<PackageReference Include="NPOI" Version="2.7.4" />
		<PackageReference Include="Volo.Abp.Account.Application" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.BackgroundWorkers.Hangfire" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.BackgroundJobs.HangFire" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Identity.Application" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.Application" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Application" Version="8.1.4" />
		<PackageReference Include="Tsp.Zalo.Application" Version="1.0.0-prerelease-5916" />
		<PackageReference Include="Tsp.Module.Notifications.Application" Version="1.0.17" />
		<PackageReference Include="Dapper" Version="2.1.35" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="ResourcePermissions\" />
	</ItemGroup>

</Project>
