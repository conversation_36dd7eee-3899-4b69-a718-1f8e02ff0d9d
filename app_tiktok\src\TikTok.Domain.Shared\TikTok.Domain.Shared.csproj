﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFrameworks>net8.0</TargetFrameworks>
		<Nullable>enable</Nullable>
		<RootNamespace>TikTok</RootNamespace>
		<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Volo.Abp.Identity.Domain.Shared" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.BackgroundJobs.Domain.Shared" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.AuditLogging.Domain.Shared" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.TenantManagement.Domain.Shared" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.FeatureManagement.Domain.Shared" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.PermissionManagement.Domain.Shared" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.SettingManagement.Domain.Shared" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.OpenIddict.Domain.Shared" Version="8.1.4" />
		<PackageReference Include="Tsp.Zalo.Domain.Shared" Version="1.0.0-prerelease-5916" />
    <PackageReference Include="Tsp.Module.Notifications.Domain.Shared" Version="1.0.17" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Localization\TikTok\*.json" />
		<Content Remove="Localization\TikTok\*.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="8.0.6" />
	</ItemGroup>

</Project>
