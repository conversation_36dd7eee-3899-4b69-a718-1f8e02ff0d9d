'use strict';

$(function () {
    // ✅ THÊM: Unified Loading Manager
    class LoadingManager {
        static showLoading(
            containerId,
            message = 'Đang tải dữ liệu...',
            color = 'primary'
        ) {
            const container = document.getElementById(containerId);
            if (container) {
                // ✅ FIXED: Use flexbox centering for consistent alignment
                container.innerHTML = `
                    <div class="d-flex justify-content-center align-items-center p-4" style="min-height: 200px;">
                        <div class="text-center">
                            <div class="spinner-border text-${color}" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">${message}</p>
                        </div>
                    </div>
                `;
            }
        }

        static hideLoading(containerId, content = '') {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = content;
            }
        }

        static showError(containerId, message = 'Có lỗi xảy ra') {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="text-center p-4 text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p class="mt-2">${message}</p>
                    </div>
                `;
            }
        }

        static showSuccess(containerId, message = 'Tải thành công') {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="text-center p-4 text-success">
                        <i class="fas fa-check-circle"></i>
                        <p class="mt-2">${message}</p>
                    </div>
                `;
            }
        }
    }

    class TiktokGmvMaxCampaignPivotTable {
        constructor() {
            this.pivotTableObj = null;
            this.currencyManager = new Currency('campaign');
            this.currentCurrency = this.currencyManager.getCurrentCurrency();
            this.availableCurrencies = ['USD', 'VND'];

            // Use shared batched refresh
            this.pendingRefresh = false;
            this.refreshTimeout = null;

            // Shared cached lookups/hash
            this.cachedLookups = null;
            this.lastDataHash = null;

            this.performanceMetrics = {
                refreshCount: 0,
                dataProcessingTime: 0,
                lastRefreshTime: null,
                totalProcessedRecords: 0,
            };

            this.dataBoundTimeout = null;

            // Get alert thresholds from window.CampaignAlertThresholds
            this.alertThresholds = window.CampaignAlertThresholds || {
                roasCritical: 1.5, // ROI < 1.5
                roasLow: 2.0, // ROI < 2.0
                roasGood: 3.0, // ROI > 3.0
                tacosHigh: 30, // TACOS > 30%
                tacosMedium: 20, // TACOS > 20%
                budgetUtilizationHigh: 80, // Budget > 80%
                budgetUtilizationMedium: 60, // Budget > 60%
            };

            this.heatmapThresholds = {
                // ROI thresholds
                roasEmergency: 1.0, // Dark red - campaign losing money
                roasCritical: this.alertThresholds.roasCritical, // Red - critical ROI
                roasLow: this.alertThresholds.roasLow, // Orange - low ROI
                roasGood: this.alertThresholds.roasGood, // Green - good ROI
                roasExcellent: 5.0, // Dark green - excellent ROI
                // Colors for heatmap visualization
                colors: {
                    emergency: { bg: '#d32f2f', color: '#ffffff' }, // Dark red
                    critical: { bg: '#f44336', color: '#ffffff' }, // Red
                    low: { bg: '#ff9800', color: '#ffffff' }, // Orange
                    warning: { bg: '#ffc107', color: '#212121' }, // Yellow
                    good: { bg: '#4caf50', color: '#ffffff' }, // Green
                    excellent: { bg: '#2e7d32', color: '#ffffff' }, // Dark green
                    veryHigh: { bg: '#1b5e20', color: '#ffffff' }, // Very dark green
                },
            };
        }

        // ✅ THÊM: Loading management methods cho Pivot Table
        showPivotLoading() {
            LoadingManager.showLoading(
                'pivot-loading-indicator',
                'Đang tải bảng phân tích...',
                'primary'
            );
            const pivotContainer = document.getElementById(
                'FactGmvMaxCampaignPivotTable'
            );
            if (pivotContainer) {
                pivotContainer.style.display = 'none';
            }
        }

        hidePivotLoading() {
            const loadingIndicator = document.getElementById(
                'pivot-loading-indicator'
            );
            const pivotContainer = document.getElementById(
                'FactGmvMaxCampaignPivotTable'
            );

            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            if (pivotContainer) {
                pivotContainer.style.display = 'block';
            }
        }

        showPivotError(message = 'Lỗi tải bảng phân tích') {
            LoadingManager.showError('pivot-loading-indicator', message);
            // ✅ FIX: Also hide the pivot container on error
            const pivotContainer = document.getElementById(
                'FactGmvMaxCampaignPivotTable'
            );
            if (pivotContainer) {
                pivotContainer.style.display = 'none';
            }
        }

        // ✅ THÊM: Show loading overlay for refresh (không ẩn pivot table)
        showPivotRefreshLoading() {
            const pivotContainer = document.getElementById(
                'FactGmvMaxCampaignPivotTable'
            );
            if (pivotContainer) {
                // Create loading overlay
                const loadingOverlay = document.createElement('div');
                loadingOverlay.id = 'pivot-refresh-loading-overlay';
                loadingOverlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                    border-radius: 8px;
                `;

                loadingOverlay.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Đang làm mới dữ liệu...</p>
                    </div>
                `;

                // Make pivot container relative positioned
                pivotContainer.style.position = 'relative';

                // Add overlay
                pivotContainer.appendChild(loadingOverlay);
            }
        }

        // ✅ THÊM: Hide loading overlay for refresh
        hidePivotRefreshLoading() {
            const loadingOverlay = document.getElementById(
                'pivot-refresh-loading-overlay'
            );
            if (loadingOverlay) {
                loadingOverlay.remove();
            }
        }

        batchedRefresh(changes = {}) {
            if (
                !this._batchedRefresh &&
                window.dataProcessingUtils &&
                window.dataProcessingUtils.createBatchedRefresh
            ) {
                this._batchedRefresh =
                    window.dataProcessingUtils.createBatchedRefresh(
                        this.pivotTableObj,
                        this.performanceMetrics
                    );
            }
            // If pivot not ready yet, fallback to local minimal apply
            if (!this._batchedRefresh) {
                if (changes.values && this.pivotTableObj)
                    this.pivotTableObj.dataSourceSettings.values =
                        changes.values;
                if (changes.dataSource && this.pivotTableObj)
                    this.pivotTableObj.dataSourceSettings.dataSource =
                        changes.dataSource;
                if (changes.formatSettings && this.pivotTableObj)
                    this.pivotTableObj.dataSourceSettings.formatSettings =
                        changes.formatSettings;
                if (changes.conditionalFormatSettings && this.pivotTableObj)
                    this.pivotTableObj.dataSourceSettings.conditionalFormatSettings =
                        changes.conditionalFormatSettings;
                if (changes.filterSettings && this.pivotTableObj)
                    this.pivotTableObj.dataSourceSettings.filterSettings =
                        changes.filterSettings;
                this.pivotTableObj &&
                    this.pivotTableObj.refresh &&
                    this.pivotTableObj.refresh();
                return;
            }
            this._batchedRefresh(changes);
        }

        generateDataHash(data) {
            if (!data) return null;

            const factCount = data.factGmvMaxCampaigns?.length || 0;
            const dimCounts = [
                (data.dimAdAccounts || data.DimAdAccounts)?.length || 0,
                (data.dimBusinessCenters || data.DimBusinessCenters)?.length ||
                    0,
                (data.dimStores || data.DimStores)?.length || 0,
                (data.dimProducts || data.DimProducts)?.length || 0,
                (data.dimDates || data.DimDates)?.length || 0,
            ].join('-');

            return `${factCount}_${dimCounts}`;
        }

        createCachedLookups(data) {
            if (!data) return {};

            const dataHash = this.generateDataHash(data);
            if (this.lastDataHash === dataHash && this.cachedLookups) {
                return this.cachedLookups;
            }

            const adAccountsRaw =
                data.dimAdAccounts || data.DimAdAccounts || [];
            const businessCentersRaw =
                data.dimBusinessCenters || data.DimBusinessCenters || [];
            const storesRaw = data.dimStores || data.DimStores || [];
            const productsRaw = data.dimProducts || data.DimProducts || [];
            const datesRaw = data.dimDates || data.DimDates || [];

            const normalizeId = (x) => x?.id ?? x?.Id;

            const adAccounts = adAccountsRaw.map((a) => ({
                id: normalizeId(a),
                advertiserName:
                    a?.advertiserName ??
                    a?.AdvertiserName ??
                    a?.name ??
                    a?.Name,
                advertiserId:
                    a?.advertiserId ?? a?.AdvertiserId ?? normalizeId(a),
            }));

            const businessCenters = businessCentersRaw.map((b) => ({
                id: normalizeId(b),
                bcName: b?.bcName ?? b?.BcName ?? b?.name ?? b?.Name,
                bcId: b?.bcId ?? b?.BcId ?? normalizeId(b),
            }));

            const stores = storesRaw.map((s) => ({
                id: normalizeId(s),
                storeName: s?.storeName ?? s?.StoreName ?? s?.name ?? s?.Name,
                storeId: s?.storeId ?? s?.StoreId ?? normalizeId(s),
            }));

            const products = productsRaw.map((p) => ({
                id: normalizeId(p),
                productName:
                    p?.productName ?? p?.ProductName ?? p?.name ?? p?.Name,
            }));

            const dates = datesRaw.map((d) => ({
                id: normalizeId(d),
                fullDate: d?.fullDate ?? d?.FullDate ?? d?.date ?? d?.Date,
                dateFormat_DDMMYYYY:
                    d?.dateFormat_DDMMYYYY ?? d?.DateFormat_DDMMYYYY,
                year: d?.year ?? d?.Year,
                month: d?.month ?? d?.Month,
                monthName: d?.monthName ?? d?.MonthName,
            }));

            this.cachedLookups = {
                adAccount: this.createLookup(adAccounts, 'id'),
                businessCenter: this.createLookup(businessCenters, 'id'),
                store: this.createLookup(stores, 'id'),
                product: this.createLookup(products, 'id'),
                date: this.createLookup(dates, 'id'),
            };

            this.lastDataHash = dataHash;
            return this.cachedLookups;
        }

        async initial() {
            // ✅ PERFORMANCE: Show loading before data processing
            // Virtual scrolling and lazy loading enabled for better performance with large datasets (100k+ records)
            this.showPivotLoading();

            try {
                const dataSource = await this.extractPivotDataOptimized([
                    'campaign',
                ]);

                this.fullDataSource = dataSource;

                // Get filtered values based on permissions
                const filteredValues = await this.getCurrencyValues(
                    this.getStoredColumnAggregations()
                );

                this.pivotTableObj = new ej.pivotview.PivotView({
                    dataSourceSettings: {
                        dataSource: dataSource,
                        allowLabelFilter: false,
                        allowValueFilter: false,
                        allowMemberFilter: false,
                        enableSorting: true,
                        allowCalculatedField: true,
                        // ✅ PERFORMANCE: Enable virtual scrolling for large datasets
                        enableVirtualScrolling: true,

                        rows: [
                            {
                                name: 'BusinessCenterName',
                                caption: 'Trung tâm kinh doanh',
                                showSubTotals: true,
                            },
                            {
                                name: 'StoreName',
                                caption: 'Tên Shop',
                                showSubTotals: true,
                            },
                            {
                                name: 'CampaignName',
                                caption: 'Tên chiến dịch',
                                showSubTotals: false,
                            },
                        ],

                        columns: this.getSmartTimeColumns(),

                        values: filteredValues,

                        filters: [],

                        formatSettings: this.getCurrencyFormatSettings(),

                        calculatedFieldSettings: [
                            {
                                name: 'Revenue_Per_Click',
                                formula:
                                    '"Clicks" > 0 ? ("GrossRevenue" / "Clicks") : 0',
                                caption: 'Doanh thu mỗi click',
                            },
                            {
                                name: 'Campaign_Efficiency_Score',
                                formula:
                                    '("ROI" > 3) ? 100 : (("ROI" > 2) ? 70 : (("ROI" > 1.5) ? 40 : 20))',
                                caption: 'Điểm hiệu quả chiến dịch',
                            },
                        ],

                        expandAll: false, // ✅ Collapse rows by default for better performance

                        excludeFields: [
                            'NetCost',
                            'NetCostVND', // Legacy VND fields
                            'CostPerOrderVND', // Legacy VND fields
                            'CPMVND', // Legacy VND fields
                            'CPCVND', // Legacy VND fields
                            // Note: Keep USD fields available for calculations
                        ],

                        sortSettings: [
                            { name: 'ROAS', order: 'Descending' },
                            {
                                name:
                                    this.currentCurrency === 'VND'
                                        ? 'GrossRevenueVND'
                                        : this.currentCurrency === 'USD'
                                        ? 'GrossRevenueUSD'
                                        : 'GrossRevenue',
                                order: 'Descending',
                            },
                        ],
                    },

                    locale:
                        (window.dashboardConfig &&
                            window.dashboardConfig.locale) ||
                        'vi-VN',
                    height: 1000,
                    width: '100%',
                    showGroupingBar: false,
                    showFieldList: true,
                    allowExcelExport: true,
                    allowPdfExport: false,
                    showToolbar: true,
                    // ✅ PERFORMANCE: Enable lazy loading and virtual scrolling for better performance
                    enableVirtualScrolling: true,
                    enableLazyLoading: true,

                    // ✅ PERFORMANCE: Virtual scrolling configuration for large datasets
                    virtualScrollingSettings: {
                        enableVirtualScrolling: true,
                        allowVirtualScrolling: true,
                        virtualScrollMode: 'Both', // Both rows and columns
                        rowHeight: 30, // Fixed row height for better performance
                        columnWidth: 140, // Fixed column width
                    },

                    showValuesButton: false,
                    showRowSubTotals: false,
                    showColumnSubTotals: false,
                    showGrandTotals: false,
                    gridSettings: {
                        layout: 'Tabular',
                        columnWidth: 140,
                        allowSelection: false,
                        selectionSettings: { mode: 'Cell', type: 'Multiple' },
                        // ✅ PERFORMANCE: Optimized grid settings for large datasets
                        enableVirtualScrolling: true,
                        allowVirtualScrolling: true,
                        rowHeight: 30,
                        enableAutoFit: false, // Disable auto-fit for better performance
                        allowResizing: false, // Disable resizing for better performance
                    },

                    toolbar: [
                        'SubTotal',
                        'GrandTotal',
                        'FieldList',
                        'ConditionalFormatting',
                        'NumberFormatting',
                    ],

                    conditionalFormatSettings:
                        this.generateCampaignHeatmapFormatting(),
                    allowConditionalFormatting: true,

                    cellClick: (args) => {
                        this.handleCellClick(args);
                    },

                    dataBound: () => {
                        if (this.dataBoundTimeout) {
                            clearTimeout(this.dataBoundTimeout);
                        }
                        this.dataBoundTimeout = setTimeout(() => {
                            this.updateCampaignInsights();
                        }, 200); // 200ms debounce
                    },

                    // Chart settings for campaign analysis
                    chartSettings: {
                        chartSeries: {
                            type: 'Column',
                            animation: { enable: true },
                        },
                        primaryYAxis: {
                            title: 'Doanh thu (USD)',
                            labelFormat: 'C0',
                        },
                    },
                });

                // Render to container
                this.pivotTableObj.appendTo('#FactGmvMaxCampaignPivotTable');

                // ✅ PERFORMANCE: Optimized refresh for virtual scrolling
                setTimeout(() => {
                    if (this.pivotTableObj && this.pivotTableObj.refresh) {
                        this.pivotTableObj.refresh();
                    }
                }, 200); // Increased delay for virtual scrolling initialization

                await this.applyInitialValuesConfiguration();

                // ✅ THÊM: Hide loading after successful rendering
                this.hidePivotLoading();

                setTimeout(() => {
                    if (typeof populateFilterOptions === 'function') {
                        populateFilterOptions();
                    }
                }, 100); // Reduced from 500ms to 100ms
            } catch (error) {
                // ✅ THÊM: Show error on failure
                this.showPivotError('Lỗi tải bảng phân tích: ' + error.message);
            }
        }

        async applyInitialValuesConfiguration() {
            // Get saved values from localStorage
            let savedValues = localStorage.getItem(
                'campaignValueSelectedState'
            );
            let desiredValues;

            if (savedValues) {
                try {
                    desiredValues = JSON.parse(savedValues);
                } catch (e) {
                    // Use defaults on parse error
                    desiredValues = [
                        'GrossRevenue',
                        'Cost',
                        'ROI',
                        'TACOS',
                        'Orders',
                        'CostPerOrder',
                    ];
                }
            } else {
                desiredValues = [
                    'GrossRevenue',
                    'Cost',
                    'ROI',
                    'TACOS',
                    'Orders',
                    'CostPerOrder',
                ];
            }

            // Apply values directly to pivot table configuration without triggering refresh
            if (desiredValues && desiredValues.length > 0) {
                const currentAggregations = this.getStoredColumnAggregations();
                const newValues = await this.getCurrencyValues(
                    currentAggregations
                );
                const filteredValues = newValues.filter((value) =>
                    desiredValues.includes(value.name)
                );

                this.pivotTableObj.dataSourceSettings.values = filteredValues;
            }
        }

        async extractPivotDataOptimized(
            includes = ['campaign'],
            dataToUse = null
        ) {
            let sourceData = dataToUse;

            if (!sourceData) {
                try {
                    // Show loading indicator
                    this.showDataLoadingIndicator();

                    // ✅ REMOVED: Permission checks - backend will handle permissions
                    // await window.PermissionHelper.waitForABP();
                    // const permissions = window.PermissionHelper.getPermissions('campaign');
                    // if (!permissions.viewSpending && !permissions.viewMetrics && !permissions.viewAll) {
                    //     this.hideDataLoadingIndicator();
                    //     window.PermissionHelper.showPermissionDeniedMessage('campaign');
                    //     return [];
                    // }

                    // Fetch data from API
                    const apiUrl = this.getApiUrl();
                    const response = await fetch(apiUrl);
                    if (!response.ok) {
                        throw new Error(
                            `HTTP error! status: ${response.status}`
                        );
                    }
                    sourceData = await response.json();

                    // Hide loading indicator
                    this.hideDataLoadingIndicator();
                } catch (error) {
                    this.hideDataLoadingIndicator();
                    showToast(
                        'error',
                        'Lỗi tải dữ liệu từ API: ' + error.message
                    );
                    return [];
                }
            }

            if (!sourceData?.factGmvMaxCampaigns) {
                return [];
            }

            const facts = sourceData.factGmvMaxCampaigns;
            if (!facts || facts.length === 0) {
                return [];
            }

            const transformedData = [];

            // ✅ PERFORMANCE: Increased batch size for better performance with large datasets
            const BATCH_SIZE = 2000;

            const lookups = this.createCachedLookups(sourceData);

            const processingStart = performance.now();

            for (let i = 0; i < facts.length; i += BATCH_SIZE) {
                const batch = facts.slice(i, i + BATCH_SIZE);

                const batchResults = batch.map((fact) => {
                    const dateLookup = (lookups && lookups.date) || {};
                    const adAccountLookup =
                        (lookups && lookups.adAccount) || {};
                    const businessCenterLookup =
                        (lookups && lookups.businessCenter) || {};
                    const storeLookup = (lookups && lookups.store) || {};
                    const productLookup = (lookups && lookups.product) || {};

                    const dateInfo = dateLookup[fact.dimDateId] || {};
                    const adAccountInfo =
                        adAccountLookup[fact.dimAdAccountId] || {};
                    const businessCenterInfo =
                        businessCenterLookup[fact.dimBusinessCenterId] || {};
                    const storeInfo = storeLookup[fact.dimStoreId] || {};
                    const productInfo = fact.dimProductId
                        ? productLookup[fact.dimProductId]
                        : null;

                    const transformedRecord = {
                        // Core campaign identification
                        CampaignId: fact.campaignId,
                        CampaignName: fact.campaignName || fact.campaignId,
                        ShoppingAdsType: fact.shoppingAdsType || 'UNKNOWN',
                        OperationStatus: fact.operationStatus || 'UNKNOWN',

                        // Business context
                        BusinessCenterName:
                            businessCenterInfo.bcName || 'Unknown BC',
                        BusinessCenterId: businessCenterInfo.bcId || '',
                        AdAccountName: adAccountInfo.advertiserName || '',
                        AdAccountId: adAccountInfo.advertiserId || '',

                        // Store and Product info
                        StoreName: storeInfo.storeName || 'Unknown Store',
                        StoreId: fact.storeId,
                        ProductName: productInfo?.productName || '',
                        ProductId: fact.productId || '',

                        // Date context
                        Date: dateInfo.fullDate || fact.date,
                        DateFormatted:
                            dateInfo.dateFormat_DDMMYYYY ||
                            formatDate(fact.date),
                        DateKey: fact.dimDateId,
                        Year:
                            dateInfo.year || new Date(fact.date).getFullYear(),
                        Month:
                            dateInfo.month ||
                            new Date(fact.date).getMonth() + 1,
                        MonthName:
                            dateInfo.monthName || getMonthName(fact.date),
                        WeekDay: getVietnameseWeekday(new Date(fact.date)),
                        WeekOfYear: getWeekOfYear(fact.date),
                        WeekOfMonth: getWeekOfMonth(fact.date),
                        WeekStartDate: getWeekStartDate(fact.date),
                        WeekEndDate: getWeekEndDate(fact.date),
                        Quarter: getQuarter(fact.date),

                        // ✅ New formatted fields for smart grouping
                        WeekMonthYear: getWeekMonthYear(fact.date),
                        MonthYear: getMonthYear(fact.date),
                        YearFormatted: getYearFormatted(fact.date),

                        // Financial metrics with currency support
                        Cost: this.getCurrencyValue(fact, 'cost'),
                        NetCost: this.getCurrencyValue(fact, 'netCost'),
                        GrossRevenue: this.getCurrencyValue(
                            fact,
                            'grossRevenue'
                        ),
                        AdsRevenue: this.getCurrencyValue(fact, 'adsRevenue'),
                        OrganicRevenue: this.getCurrencyValue(
                            fact,
                            'organicRevenue'
                        ),
                        Budget: this.getCurrencyValue(fact, 'budget'),

                        // Performance metrics
                        Orders: fact.orders || 0,
                        CostPerOrder: this.getCurrencyValue(
                            fact,
                            'costPerOrder'
                        ),
                        ROI: fact.roas || 0,
                        TACOS: fact.tacos || 0,

                        // Campaign-level metrics for new entity structure
                        BidType: fact.bidType || '',
                        RoasBid: fact.roasBid || 0,
                        TargetRoiBudget: this.getCurrencyValue(
                            fact,
                            'targetRoiBudget'
                        ),
                        MaxDeliveryBudget: this.getCurrencyValue(
                            fact,
                            'maxDeliveryBudget'
                        ),
                        ScheduleType: fact.scheduleType || '',

                        // LIVE campaign specific fields
                        TtAccountName: fact.ttAccountName || '',
                        LiveViews: fact.liveViews || 0,
                        CostPerLiveView: this.getCurrencyValue(
                            fact,
                            'costPerLiveView'
                        ),
                        TenSecondLiveViews: fact.tenSecondLiveViews || 0,
                        LiveFollows: fact.liveFollows || 0,

                        Currency: fact.currency || 'USD',
                    };

                    this.calculateCampaignMetricsInline(transformedRecord);
                    this.addCampaignClassificationsInline(transformedRecord);

                    return transformedRecord;
                });

                transformedData.push(...batchResults);

                if (i + BATCH_SIZE < facts.length) {
                    await new Promise((resolve) => setTimeout(resolve, 0));
                }
            }

            // ✅ PERFORMANCE: Store processing metrics
            const processingDuration = performance.now() - processingStart;
            this.performanceMetrics.dataProcessingTime = processingDuration;
            this.performanceMetrics.totalProcessedRecords =
                transformedData.length;

            return transformedData;
        }

        // ✅ Helper methods for API loading
        getApiUrl() {
            const config = window.dashboardConfig || {};
            const fromDate =
                config.fromDate ||
                new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                    .toISOString()
                    .split('T')[0];
            const toDate =
                config.toDate || new Date().toISOString().split('T')[0];
            // Get currency from localStorage
            const currency =
                (typeof localStorage !== 'undefined'
                    ? localStorage.getItem('tiktok_currency')
                    : null) || 'USD';

            const url = `/api/fact-gmv-max-campaign/data?fromDate=${fromDate}&toDate=${toDate}&currency=${currency}`;
            return url;
        }

        showDataLoadingIndicator() {
            const indicator = document.getElementById('data-loading-indicator');
            if (indicator) {
                indicator.style.display = 'block';
            }
        }

        hideDataLoadingIndicator() {
            const indicator = document.getElementById('data-loading-indicator');
            if (indicator) {
                indicator.style.display = 'none';
            }
        }

        // ✅ Error message function removed - using shared common-utils.js instead

        formatCurrency(amount, symbol = null) {
            // ✅ Use Currency class for consistent formatting
            if (this.currencyManager) {
                const formatted = this.currencyManager.formatCurrency(amount);
                return formatted;
            }

            // ✅ Fallback if Currency class is not available
            if (!amount) return this.currentCurrency === 'VND' ? '₫0' : '$0';
            const isVnd = this.currentCurrency === 'VND';
            const locale = isVnd ? 'vi-VN' : 'en-US';
            const formatted = new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: isVnd ? 'VND' : 'USD',
                maximumFractionDigits: 0,
                minimumFractionDigits: 0,
            })
                .format(amount)
                .replace('₫', 'đ');
            return formatted;
        }

        // ✅ Refresh currency from localStorage
        async refreshCurrency() {
            if (this.currencyManager) {
                this.currentCurrency =
                    this.currencyManager.getStoredCurrency() || 'USD';

                // ✅ Clear cached dashboard data and reload with new currency
                window.dashboardData = null;
                dashboardDataLoaded = false; // Reset flag to allow reload

                // ✅ Reload dashboard data with new currency
                if (window.CampaignDashboard) {
                    const newCurrency =
                        this.currencyManager.getStoredCurrency() || 'USD';
                    CampaignDashboard.refresh(newCurrency);
                }

                // ✅ Refresh pivot table with new currency
                await this.refreshPivotTableWithCurrency(this.currentCurrency);
            }
        }

        // ✅ Refresh pivot table with new currency
        async refreshPivotTableWithCurrency(currency) {
            if (!this.pivotTableObj) return;

            try {
                // Update format settings
                this.pivotTableObj.dataSourceSettings.formatSettings =
                    this.getCurrencyFormatSettings();

                // Preserve selected values and current aggregations
                const selectedValues = JSON.parse(
                    localStorage.getItem('campaignValueSelectedState') || '[]'
                );
                const currentAggregations = this.getStoredColumnAggregations();
                let newValues = await this.getCurrencyValues();

                // Apply aggregations
                newValues = newValues.map((v) => ({
                    name: v.name,
                    caption: v.caption,
                    type: currentAggregations[v.name] || v.type || 'Sum',
                    showSubTotals: v.showSubTotals,
                }));

                if (
                    Array.isArray(selectedValues) &&
                    selectedValues.length > 0
                ) {
                    newValues = newValues.filter((v) =>
                        selectedValues.includes(v.name)
                    );
                }

                this.pivotTableObj.dataSourceSettings.values = newValues;

                // Re-extract data with new currency
                this.pivotTableObj.dataSourceSettings.dataSource =
                    await this.extractPivotDataOptimized(['campaign']);
                this.pivotTableObj.refresh();
            } catch (error) {
                console.error(
                    'Error refreshing pivot table with currency:',
                    error
                );
            }
        }

        // ✅ Generate campaign performance heatmap
        generateCampaignHeatmapFormatting() {
            const formatSettings = [];
            const measures = ['ROI', 'TACOS'];
            const thresholds = this.heatmapThresholds;

            // ROI formatting
            formatSettings.push(
                // Emergency ROI (< 1.0) - Dark Red
                {
                    measure: 'ROI',
                    value1: 0,
                    value2: thresholds.roasEmergency,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.emergency.bg,
                        color: thresholds.colors.emergency.color,
                        fontWeight: 'bold',
                        border: '2px solid #b71c1c',
                    },
                },
                // Critical ROI (1.0 - 1.5) - Red
                {
                    measure: 'ROI',
                    value1: thresholds.roasEmergency,
                    value2: thresholds.roasCritical,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.critical.bg,
                        color: thresholds.colors.critical.color,
                        fontWeight: 'bold',
                    },
                },
                // Low ROI (1.5 - 2.0) - Orange
                {
                    measure: 'ROI',
                    value1: thresholds.roasCritical,
                    value2: thresholds.roasLow,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.low.bg,
                        color: thresholds.colors.low.color,
                        fontWeight: '500',
                    },
                },
                // Good ROI (2.0 - 3.0) - Yellow
                {
                    measure: 'ROI',
                    value1: thresholds.roasLow,
                    value2: thresholds.roasGood,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.warning.bg,
                        color: thresholds.colors.warning.color,
                    },
                },
                // Excellent ROI (3.0 - 5.0) - Green
                {
                    measure: 'ROI',
                    value1: thresholds.roasGood,
                    value2: thresholds.roasExcellent,
                    conditions: 'Between',
                    style: {
                        backgroundColor: thresholds.colors.good.bg,
                        color: thresholds.colors.good.color,
                    },
                },
                // Very High ROI (> 5.0) - Dark Green
                {
                    measure: 'ROI',
                    value1: thresholds.roasExcellent,
                    conditions: 'GreaterThan',
                    style: {
                        backgroundColor: thresholds.colors.excellent.bg,
                        color: thresholds.colors.excellent.color,
                        fontWeight: 'bold',
                    },
                }
            );

            // TACOS formatting
            formatSettings.push(
                // High TACOS (> 30%) - Red
                {
                    measure: 'TACOS',
                    value1: this.alertThresholds.tacosHigh,
                    conditions: 'GreaterThan',
                    style: {
                        backgroundColor: '#ffebee',
                        color: '#c62828',
                        fontWeight: 'bold',
                    },
                },
                // Medium TACOS (20% - 30%) - Yellow
                {
                    measure: 'TACOS',
                    value1: this.alertThresholds.tacosMedium,
                    value2: this.alertThresholds.tacosHigh,
                    conditions: 'Between',
                    style: {
                        backgroundColor: '#fff8e1',
                        color: '#ef6c00',
                    },
                }
            );

            return formatSettings;
        }

        // ✅ Inline optimized metrics calculation
        calculateCampaignMetricsInline(record) {
            // ✅ Single-pass calculation for better performance
            const clicks = record.Clicks || 0;
            const impressions = record.Impressions || 0;
            const grossRevenue = record.GrossRevenue || 0;
            const cost = record.Cost || 0;
            const orders = record.Orders || 0;

            // Conversion rate
            record.ConversionRate =
                clicks > 0
                    ? parseFloat(((orders / clicks) * 100).toFixed(2))
                    : 0;

            // Revenue per impression
            record.RevenuePerImpression =
                impressions > 0
                    ? parseFloat((grossRevenue / impressions).toFixed(4))
                    : 0;

            // Profit margin
            record.ProfitMargin =
                grossRevenue > 0
                    ? parseFloat(
                          (
                              ((grossRevenue - cost) / grossRevenue) *
                              100
                          ).toFixed(2)
                      )
                    : 0;
        }

        // ✅ Inline optimized classifications
        addCampaignClassificationsInline(record) {
            const roas = record.ROI || 0;
            const tacosPct = (record.TACOS || 0) * 100;

            // ✅ Inline calculations for better performance
            record.ROIStatus =
                roas >= this.alertThresholds.roasGood
                    ? 'Excellent'
                    : roas >= this.alertThresholds.roasLow
                    ? 'Good'
                    : roas >= this.alertThresholds.roasCritical
                    ? 'Warning'
                    : 'Critical';

            record.TACOSStatus =
                tacosPct >= this.alertThresholds.tacosHigh
                    ? 'High'
                    : tacosPct >= this.alertThresholds.tacosMedium
                    ? 'Medium'
                    : 'Low';

            record.CampaignHealth =
                roas >= 3.0 && tacosPct <= 20
                    ? 'Excellent'
                    : roas >= 2.0 && tacosPct <= 30
                    ? 'Good'
                    : roas >= 1.5
                    ? 'Fair'
                    : 'Poor';

            record.AlertLevel =
                roas < 1.5 || tacosPct > this.alertThresholds.tacosHigh
                    ? 'High'
                    : roas < 2.0 || tacosPct > this.alertThresholds.tacosMedium
                    ? 'Medium'
                    : 'Low';
        }

        // ✅ NEW: Dynamic currency support with proper field selection
        getCurrencyValue(fact, fieldName) {
            const currency = this.currentCurrency;

            // ✅ CURRENCY FIELD SELECTION LOGIC
            // If currency is VND, use VND field; if USD, use USD field; otherwise use original field
            if (currency === 'VND') {
                const vndValue = fact[`${fieldName}VND`];
                if (vndValue !== undefined) {
                    return vndValue || 0;
                }
            } else if (currency === 'USD') {
                const usdValue = fact[`${fieldName}USD`];
                if (usdValue !== undefined) {
                    return usdValue || 0;
                }
            }

            // Fallback to original field
            const originalValue = fact[fieldName];
            if (originalValue !== undefined) {
                return originalValue || 0;
            }

            return 0;
        }

        getCurrencyFormatSettings() {
            const currency = this.currentCurrency;
            const currencySymbol = currency === 'VND' ? '₫' : '$';

            // ✅ CURRENCY FIELD SELECTION LOGIC
            const getCurrencyField = (originalField, vndField, usdField) => {
                // Check if currency fields exist in data
                const hasData =
                    this.fullDataSource && this.fullDataSource.length > 0;
                if (!hasData) {
                    return originalField;
                }

                const sampleRecord = this.fullDataSource[0];

                if (
                    currency === 'VND' &&
                    sampleRecord[vndField] !== undefined
                ) {
                    return vndField;
                }
                if (
                    currency === 'USD' &&
                    sampleRecord[usdField] !== undefined
                ) {
                    return usdField;
                }

                return originalField;
            };

            return [
                {
                    name: 'Cost',
                    format: 'N0',
                    suffix: ` ${currencySymbol}`,
                },
                {
                    name: 'GrossRevenue',
                    format: 'N0',
                    suffix: ` ${currencySymbol}`,
                },
                {
                    name: 'CostPerOrder',
                    format: 'N0',
                    suffix: ` ${currencySymbol}`,
                },
                {
                    name: 'ROAS',
                    format: 'N2',
                    suffix: 'x',
                },
                {
                    name: 'TACOS',
                    format: 'N1',
                    suffix: '%',
                },
            ];
        }

        async getCurrencyValues(columnAggregations = null) {
            // ✅ NEW: Dynamic currency support with proper field selection
            const currency = this.currentCurrency;
            const currencySymbol = currency === 'VND' ? '₫' : '$';
            const aggregations =
                columnAggregations || this.getStoredColumnAggregations();

            // Get permissions to filter fields - wait for ABP to be ready
            await window.PermissionHelper.waitForABP();
            const permissions =
                window.PermissionHelper.getPermissions('campaign');

            // ✅ CURRENCY FIELD SELECTION LOGIC
            // If currency is VND, use VND fields; if USD, use USD fields; otherwise use original fields
            const getCurrencyField = (originalField, vndField, usdField) => {
                // Check if currency fields exist in data
                const hasData =
                    this.fullDataSource && this.fullDataSource.length > 0;
                if (!hasData) {
                    return originalField;
                }

                const sampleRecord = this.fullDataSource[0];

                if (
                    currency === 'VND' &&
                    sampleRecord[vndField] !== undefined
                ) {
                    return vndField;
                }
                if (
                    currency === 'USD' &&
                    sampleRecord[usdField] !== undefined
                ) {
                    return usdField;
                }

                return originalField;
            };

            // ✅ SIMPLIFIED: Use original fields first to test
            const allValues = [
                // Revenue Metrics
                {
                    name: 'GrossRevenue',
                    caption: `Tổng doanh thu (${currencySymbol})`,
                    type: aggregations['GrossRevenue'] || 'Sum',
                    showSubTotals: true,
                    category: 'metrics',
                },

                // Cost Metrics
                {
                    name: 'Cost',
                    caption: `Chi phí quảng cáo (${currencySymbol})`,
                    type: aggregations['Cost'] || 'Sum',
                    showSubTotals: true,
                    category: 'spending',
                },
                {
                    name: 'CostPerOrder',
                    caption: `Chi phí mỗi đơn (${currencySymbol})`,
                    type: aggregations['CostPerOrder'] || 'Avg',
                    showSubTotals: false,
                    category: 'spending',
                },

                // Performance Metrics
                {
                    name: 'ROAS',
                    caption: 'ROAS (Tỷ lệ hoàn vốn đầu tư)',
                    type: aggregations['ROAS'] || 'Avg',
                    showSubTotals: true,
                    category: 'metrics',
                },
                {
                    name: 'TACOS',
                    caption: 'TACOS (Tỷ lệ chi phí quảng cáo trên doanh thu)',
                    type: aggregations['TACOS'] || 'Avg',
                    showSubTotals: true,
                    category: 'restricted',
                },

                // Volume Metrics
                {
                    name: 'Orders',
                    caption: 'Số đơn hàng',
                    type: aggregations['Orders'] || 'Sum',
                    showSubTotals: true,
                    category: 'metrics',
                },
            ];

            // Filter values based on permissions
            const filteredValues = allValues.filter((value) => {
                if (permissions.viewAll || permissions.viewAllAdvertisers)
                    return true;
                if (
                    value.category === 'spending' &&
                    (permissions.viewSpending || permissions.viewAllAdvertisers)
                )
                    return true;
                if (
                    value.category === 'metrics' &&
                    (permissions.viewMetrics || permissions.viewAllAdvertisers)
                )
                    return true;
                if (
                    value.category === 'restricted' &&
                    (permissions.viewAll || permissions.viewAllAdvertisers)
                )
                    return true;
                return false;
            });

            return filteredValues;
        }

        // ✅ NEW: Get stored column aggregations from localStorage (GIỐNG HỆT FACTBALANCE)
        getStoredColumnAggregations() {
            const stored = localStorage.getItem('campaignColumnAggregations');
            if (stored) {
                try {
                    return JSON.parse(stored);
                } catch (e) {
                    // Use defaults on parse error
                }
            }

            // Return default aggregations for campaigns
            return {
                Cost: 'Sum',
                GrossRevenue: 'Sum',
                Orders: 'Sum',
                ROI: 'Avg',
                TACOS: 'Avg',
                CostPerOrder: 'Avg',
            };
        }

        // ✅ NEW: Store column aggregations to localStorage (GIỐNG HỆT FACTBALANCE)
        storeColumnAggregations(aggregations) {
            localStorage.setItem(
                'campaignColumnAggregations',
                JSON.stringify(aggregations)
            );
        }

        // ✅ Helper methods
        createLookup(array, keyField) {
            if (
                window.dataProcessingUtils &&
                window.dataProcessingUtils.createLookup
            ) {
                return window.dataProcessingUtils.createLookup(array, keyField);
            }
            if (!array) return {};
            const lookup = {};
            array.forEach((item) => {
                lookup[item[keyField]] = item;
            });
            return lookup;
        }

        // ✅ NEW: Update pivot table with column aggregations
        async updatePivotTableWithColumnAggregations() {
            if (!this.pivotTableObj) return;

            try {
                const currentAggregations = this.getStoredColumnAggregations();
                const selectedValues = JSON.parse(
                    localStorage.getItem('campaignValueSelectedState') || '[]'
                );

                if (selectedValues.length === 0) {
                    console.warn('No values selected for pivot table');
                    return;
                }

                // Create new value definitions with current column aggregations
                const allValues = await this.getCurrencyValues(
                    currentAggregations
                );
                const newValues = allValues.filter((v) =>
                    selectedValues.includes(v.name)
                );

                // ✅ PERFORMANCE: Use batched refresh for pivot table updates
                this.batchedRefresh({ values: newValues });

                // ✅ No longer using campaignSummarySections - new dashboard handles this automatically
            } catch (error) {
                console.error(
                    'Failed to update pivot table with aggregations:',
                    error
                );
            }
        }

        // ✅ NEW: Reset all column aggregations (GIỐNG HỆT FACTBALANCE)
        resetAllColumnAggregations() {
            const defaultAggregations = {
                Cost: 'Sum',
                GrossRevenue: 'Sum',
                Orders: 'Sum',
                ROI: 'Avg',
                TACOS: 'Avg',
                CostPerOrder: 'Avg',
            };

            this.storeColumnAggregations(defaultAggregations);
            this.updatePivotTableWithColumnAggregations();
        }

        // ✅ Date helper functions removed - using shared date-helper.js instead

        // ✅ NEW: Currency switching support like FactBalance
        async switchCurrency(newCurrency) {
            if (!this.availableCurrencies.includes(newCurrency)) {
                return;
            }
            this.currentCurrency = newCurrency;

            // ✅ NEW: Store the currency preference
            this.storeCurrency(newCurrency);

            // Preserve user's selected values and per-column aggregations
            const selectedValues = JSON.parse(
                localStorage.getItem('campaignValueSelectedState') || '[]'
            );
            const currentAggregations = this.getStoredColumnAggregations();
            let newValues = await this.getCurrencyValues(currentAggregations);
            if (Array.isArray(selectedValues) && selectedValues.length > 0) {
                newValues = newValues.filter((v) =>
                    selectedValues.includes(v.name)
                );
            }

            // Refresh data with new currency and clear any lingering filter settings
            this.fullDataSource = await this.extractPivotDataOptimized([
                'campaign',
            ]);

            // ✅ PERFORMANCE: Use batched refresh for multiple changes at once
            this.batchedRefresh({
                formatSettings: this.getCurrencyFormatSettings(),
                values: newValues,
                dataSource: this.fullDataSource,
                filterSettings: [],
            });

            // Update campaign insights
            this.updateCampaignInsights();

            // Update heatmap thresholds for new currency
            this.updateHeatmapThresholdsForCurrency();
        }

        // ✅ NEW: Store currency preference
        storeCurrency(currency) {
            if (this.currencyManager) {
                this.currencyManager.setStoredCurrency(currency);
            }
        }

        // ✅ NEW: Update heatmap thresholds for current currency
        updateHeatmapThresholdsForCurrency() {
            // For now, keep the same thresholds for both currencies
            // TODO: Implement currency-specific thresholds if needed

            try {
                // ✅ PERFORMANCE: Use batched refresh for conditional formatting
                this.batchedRefresh({
                    conditionalFormatSettings:
                        this.generateCampaignHeatmapFormatting(),
                });
            } catch (error) {
                console.error(
                    'Error generating heatmap conditional formatting:',
                    error
                );
            }
        }

        // ✅ Export and view methods
        exportToExcel(fileName = 'TikTok_GMV_Max_Campaign_Analysis') {
            if (this.pivotTableObj) {
                this.pivotTableObj.excelExport({
                    fileName: `${fileName}_${new Date()
                        .toISOString()
                        .slice(0, 10)}.xlsx`,
                    includeHeader: true,
                });
            }
        }

        // ✅ Smart export with full data
        async exportToExcelSmart(
            fileName = 'TikTok_GMV_Max_Campaign_Analysis'
        ) {
            if (!this.pivotTableObj) return;

            try {
                // Show progress
                this.showExportProgress('Đang xuất dữ liệu đầy đủ...');

                // Save current state
                const originalExpandAll =
                    this.pivotTableObj.dataSourceSettings.expandAll;

                // Expand all data
                this.pivotTableObj.dataSourceSettings.expandAll = true;
                this.pivotTableObj.refresh();

                // Wait for expansion
                await new Promise((resolve) => setTimeout(resolve, 1500));

                // Export
                this.pivotTableObj.excelExport({
                    fileName: `${fileName}_${new Date()
                        .toISOString()
                        .slice(0, 10)}.xlsx`,
                    includeHeader: true,
                });

                // Restore state
                this.pivotTableObj.dataSourceSettings.expandAll =
                    originalExpandAll;
                this.pivotTableObj.refresh();

                this.hideExportProgress();
                showToast('success', 'Đã xuất dữ liệu đầy đủ sang Excel!');
            } catch (error) {
                this.hideExportProgress();
                showToast('error', 'Lỗi xuất dữ liệu: ' + error.message);
            }
        }

        // ✅ Progress UI methods
        showExportProgress(message) {
            const progressHtml = `
                <div id="export-progress-modal" class="position-fixed top-50 start-50 translate-middle" 
                     style="z-index: 9999; background: rgba(0,0,0,0.8); padding: 20px; border-radius: 8px;">
                    <div class="text-center text-white">
                        <div class="spinner-border spinner-border-sm mb-2"></div>
                        <div>${message}</div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', progressHtml);
        }

        hideExportProgress() {
            const modal = document.getElementById('export-progress-modal');
            if (modal) modal.remove();
        }

        exportToPdf(fileName = 'TikTok_GMV_Max_Campaign_Report') {
            if (this.pivotTableObj) {
                this.pivotTableObj.pdfExport({
                    fileName: `${fileName}_${new Date()
                        .toISOString()
                        .slice(0, 10)}.pdf`,
                    includeHeader: true,
                });
            }
        }

        showChart() {
            if (this.pivotTableObj) {
                this.pivotTableObj.displayOption.view = 'Chart';
                this.pivotTableObj.chartSettings.chartSeries.type = 'Column';
                // ✅ PERFORMANCE: Use batched refresh for view changes
                this.batchedRefresh({});
            }
        }

        showGrid() {
            if (this.pivotTableObj) {
                this.pivotTableObj.displayOption.view = 'Grid';
                // ✅ PERFORMANCE: Use batched refresh for view changes
                this.batchedRefresh({});
            }
        }

        async refreshData(newData) {
            if (this.pivotTableObj && newData) {
                // Clear cached dashboard data to force refresh
                window.dashboardData = null;
                // ✅ PERFORMANCE: Use cached data processing and batched refresh
                const processedData = await this.extractPivotDataOptimized(
                    ['campaign'],
                    newData
                );
                this.batchedRefresh({ dataSource: processedData });
                this.updateCampaignInsights();
            }
        }

        // ✅ THÊM: Refresh pivot data với filters hiện tại
        async refreshPivotDataWithFilters() {
            if (!this.pivotTableObj) {
                throw new Error('Pivot table chưa được khởi tạo');
            }

            try {
                // ✅ FIXED: Show loading overlay instead of hiding pivot table
                this.showPivotRefreshLoading();

                // Get current filter parameters
                const filterParams = this.getCurrentFilterParams();

                // Build API URL with current filters
                const apiUrl = this.buildApiUrlWithFilters(filterParams);

                // Fetch new data from API
                const response = await fetch(apiUrl);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const newData = await response.json();

                // Clear cached data to force refresh
                window.dashboardData = null;
                window.initialGmvMaxCampaignData = newData;

                // Process new data
                const processedData = await this.extractPivotDataOptimized(
                    ['campaign'],
                    newData
                );

                // ✅ FIXED: Update pivot table data without recreating the table
                this.pivotTableObj.dataSourceSettings.dataSource =
                    processedData;
                this.pivotTableObj.refresh();

                // Hide loading after successful refresh
                this.hidePivotRefreshLoading();
            } catch (error) {
                this.hidePivotRefreshLoading();
                throw error;
            }
        }

        // ✅ THÊM: Get current filter parameters
        getCurrentFilterParams() {
            const params = {
                fromDate: this.getCurrentFromDate(),
                toDate: this.getCurrentToDate(),
                type: 'campaign',
            };

            // Add other filters if they exist
            if (
                window.dateRangePicker &&
                window.dateRangePicker.startDate &&
                window.dateRangePicker.endDate
            ) {
                params.fromDate = window.dateRangePicker.startDate
                    .toISOString()
                    .split('T')[0];
                params.toDate = window.dateRangePicker.endDate
                    .toISOString()
                    .split('T')[0];
            }

            // ✅ THÊM: Support for advanced filters
            this.addAdvancedFilters(params);

            return params;
        }

        // ✅ THÊM: Add advanced filters to parameters
        addAdvancedFilters(params) {
            // Business Center filter
            if (
                window.businessCenterMultiSelect &&
                window.businessCenterMultiSelect.value &&
                window.businessCenterMultiSelect.value.length > 0
            ) {
                params.businessCenterIds =
                    window.businessCenterMultiSelect.value.join(',');
            }

            // Shop filter
            if (
                window.shopMultiSelect &&
                window.shopMultiSelect.value &&
                window.shopMultiSelect.value.length > 0
            ) {
                params.storeIds = window.shopMultiSelect.value.join(',');
            }

            // Campaign Type filter
            if (
                window.campaignTypeMultiSelect &&
                window.campaignTypeMultiSelect.value &&
                window.campaignTypeMultiSelect.value.length > 0
            ) {
                params.campaignTypes =
                    window.campaignTypeMultiSelect.value.join(',');
            }

            // Performance filter
            if (
                window.performanceMultiSelect &&
                window.performanceMultiSelect.value &&
                window.performanceMultiSelect.value.length > 0
            ) {
                params.performanceLevels =
                    window.performanceMultiSelect.value.join(',');
            }

            // Keyword search
            const keyword = document
                .getElementById('keyword-search')
                ?.value?.trim();
            if (keyword) {
                params.keyword = keyword;
            }
        }

        // ✅ THÊM: Build API URL with filters
        buildApiUrlWithFilters(params) {
            const queryParams = new URLSearchParams();

            Object.keys(params).forEach((key) => {
                if (params[key] !== null && params[key] !== undefined) {
                    queryParams.append(key, params[key]);
                }
            });

            // Get currency from localStorage
            const currency =
                (typeof localStorage !== 'undefined'
                    ? localStorage.getItem('tiktok_currency')
                    : null) || 'USD';
            queryParams.append('currency', currency);

            return `/api/fact-gmv-max-campaign/data?${queryParams.toString()}`;
        }

        // ✅ THÊM: Get current date range
        getCurrentFromDate() {
            if (window.dateRangePicker && window.dateRangePicker.startDate) {
                return window.dateRangePicker.startDate
                    .toISOString()
                    .split('T')[0];
            }

            const config = window.dashboardConfig || {};
            return (
                config.fromDate ||
                new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                    .toISOString()
                    .split('T')[0]
            );
        }

        getCurrentToDate() {
            if (window.dateRangePicker && window.dateRangePicker.endDate) {
                return window.dateRangePicker.endDate
                    .toISOString()
                    .split('T')[0];
            }

            const config = window.dashboardConfig || {};
            return config.toDate || new Date().toISOString().split('T')[0];
        }

        handleCellClick(args) {
            if (args.currentCell && args.data) {
                const cellData = args.data[0];
                if (!cellData) return;

                // Show alerts for poor performing campaigns
                if (
                    cellData.ROIStatus === 'Critical' ||
                    cellData.ACOSStatus === 'High'
                ) {
                    this.showCampaignAlert(cellData);
                }
            }
        }

        showCampaignAlert(data) {
            const message = `
                <strong>⚠️ Cảnh báo hiệu suất chiến dịch!</strong><br>
                Chiến dịch: ${data.CampaignName}<br>
                ROI: ${data.ROI}x (${data.ROIStatus})<br>
                ACOS: ${data.ACOS}% (${data.ACOSStatus})<br>
                <em>Cần tối ưu hóa chiến dịch!</em>
            `;
            showToast('warning', message);
        }

        updateCampaignInsights() {
            // Dashboard is now independent of pivot table, no need to regenerate
            // Only update pivot table specific insights if needed
        }

        getSmartTimeColumns() {
            try {
                if (
                    window.dateRangePicker &&
                    window.dateRangePicker.startDate &&
                    window.dateRangePicker.endDate
                ) {
                    const dateRange = {
                        from: new Date(window.dateRangePicker.startDate),
                        to: new Date(window.dateRangePicker.endDate),
                    };
                    return getSmartTimeGrouping(dateRange);
                }
                return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
            } catch (error) {
                console.error('Error in getSmartTimeColumns:', error);
                return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
            }
        }
    }
    // ✅ Initialize campaign dashboard with proper timing
    let dashboardInitialized = false; // Prevent multiple initialization

    async function initializeCampaignDashboard() {
        if (dashboardInitialized) {
            return;
        }

        // Check if ej (Syncfusion) is loaded
        if (typeof ej === 'undefined') {
            setTimeout(initializeCampaignDashboard, 500);
            return;
        }

        dashboardInitialized = true; // Mark as initialized to prevent double init

        const campaignDashboard = new TiktokGmvMaxCampaignPivotTable();

        try {
            await campaignDashboard.initial();
            window.tiktokGmvMaxCampaignPivotTable = campaignDashboard;

            // ✅ Dashboard sections are initialized separately by dashboard.js
        } catch (error) {
            dashboardInitialized = false; // Reset flag on error so it can retry
        }
    }

    // ✅ Initialize pivot table when jQuery is ready
    initializeCampaignDashboard();

    // ✅ Global function to load pivot table (called from HTML)
    window.loadPivotTable = async function () {
        await initializeCampaignDashboard();
    };

    // ✅ Global function to refresh currency (called from currency selector)
    window.refreshCampaignCurrency = async function () {
        if (window.tiktokGmvMaxCampaignPivotTable) {
            await window.tiktokGmvMaxCampaignPivotTable.refreshCurrency();
        }
        // Also refresh dashboard with new currency
        if (window.CampaignDashboard) {
            const newCurrency =
                (window.sharedCurrencyManager &&
                    window.sharedCurrencyManager.getStoredCurrency &&
                    window.sharedCurrencyManager.getStoredCurrency()) ||
                (typeof localStorage !== 'undefined'
                    ? localStorage.getItem('tiktok_currency')
                    : null) ||
                'USD';
            CampaignDashboard.refresh(newCurrency);
        }
    };

    // ✅ Value Selection Modal logic for campaigns
    // ✅ NEW: Get current column aggregations from localStorage (GIỐNG HỆT FACTBALANCE)
    let currentColumnAggregations = getStoredColumnAggregations();

    // Initialize valueDefs with current column aggregations
    let valueDefs = getValueDefsWithColumnAggregations(
        currentColumnAggregations
    );

    const defaultValues = [
        'GrossRevenue',
        'Cost',
        'ROI',
        'TACOS',
        'Orders',
        'CostPerOrder',
    ];

    let valueSelectedStore =
        localStorage.getItem('campaignValueSelectedState') ?? null;
    valueSelectedStore = JSON.parse(valueSelectedStore) ?? defaultValues;
    let valueSelectedState = valueSelectedStore;

    // ✅ Value selection modal functions
    function updateValueSelection() {
        // Update value selection for pivot table
        if (window.tiktokGmvMaxCampaignPivotTable) {
            window.tiktokGmvMaxCampaignPivotTable.updatePivotTableWithColumnAggregations();
        }
    }

    // ✅ Function to update selected count display
    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll(
            '.value-checkbox:checked'
        ).length;
        const countElement = document.getElementById('selected-count');
        if (countElement) {
            countElement.textContent = selectedCount;
            countElement.className =
                selectedCount > 0
                    ? 'fw-bold text-success'
                    : 'fw-bold text-muted';
        }
    }

    // ✅ Modal event handlers
    $('#open-value-selection-modal').on('click', function () {
        // ✅ NEW: Sync column aggregations from localStorage (GIỐNG HỆT FACTBALANCE)
        currentColumnAggregations = getStoredColumnAggregations();

        // Remove unused buttons for this screen (fewer metrics; user selects manually)
        document.getElementById('select-default')?.remove();
        document.getElementById('select-all-financial')?.remove();
        document.getElementById('select-all-performance')?.remove();
        document.getElementById('select-all-engagement')?.remove();

        // ✅ NEW: Update valueDefs with current aggregations (GIỐNG HỆT FACTBALANCE)
        valueDefs = getValueDefsWithColumnAggregations(
            currentColumnAggregations
        );

        // Clear existing checkboxes
        const container = document.getElementById('value-checkbox-list');
        if (!container) {
            return;
        }
        container.innerHTML = '';

        // ✅ FIXED: Create checkboxes with individual aggregation dropdowns (GIỐNG HỆT FACTBALANCE)
        valueDefs.forEach((valueDef) => {
            const isChecked = valueSelectedState.includes(valueDef.name);
            const currentAggregation =
                currentColumnAggregations[valueDef.name] || 'Sum';

            const checkboxHtml = `
                <div class="col-6 col-md-4 mb-3">
                    <div class="card border-light">
                        <div class="card-body p-2">
                            <div class="form-check d-flex align-items-center mb-2">
                                <input class="form-check-input value-checkbox me-2" type="checkbox" value="${
                                    valueDef.name
                                }" id="cb-${valueDef.name}" ${
                isChecked ? 'checked' : ''
            }>
                                <label class="form-check-label fw-bold" for="cb-${
                                    valueDef.name
                                }">${valueDef.caption}</label>
                            </div>
                            <div class="d-flex align-items-center">
                                <label class="form-label small mb-0 me-2">Chế độ:</label>
                                <select class="form-select form-select-sm column-aggregation" data-column="${
                                    valueDef.name
                                }" style="width: auto;">
                                    <option value="Sum" ${
                                        currentAggregation === 'Sum'
                                            ? 'selected'
                                            : ''
                                    }>Tổng (Sum)</option>
                                    <option value="Avg" ${
                                        currentAggregation === 'Avg'
                                            ? 'selected'
                                            : ''
                                    }>Trung bình (Avg)</option>
                                    <option value="Max" ${
                                        currentAggregation === 'Max'
                                            ? 'selected'
                                            : ''
                                    }>Lớn nhất (Max)</option>
                                    <option value="Min" ${
                                        currentAggregation === 'Min'
                                            ? 'selected'
                                            : ''
                                    }>Nhỏ nhất (Min)</option>
                                    <option value="First" ${
                                        currentAggregation === 'First'
                                            ? 'selected'
                                            : ''
                                    }>Bản ghi đầu (First)</option>
                                    <option value="Last" ${
                                        currentAggregation === 'Last'
                                            ? 'selected'
                                            : ''
                                    }>Bản ghi cuối (Last)</option>
                                    <option value="Count" ${
                                        currentAggregation === 'Count'
                                            ? 'selected'
                                            : ''
                                    }>Đếm (Count)</option>
                                    <option value="DistinctCount" ${
                                        currentAggregation === 'DistinctCount'
                                            ? 'selected'
                                            : ''
                                    }>Đếm duy nhất (Distinct)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += checkboxHtml;
        });

        // ✅ Add event listeners for aggregation dropdowns (GIỐNG HỆT FACTBALANCE)
        document.querySelectorAll('.column-aggregation').forEach((select) => {
            select.addEventListener('change', function () {
                const columnName = this.dataset.column;
                const newAggregation = this.value;

                // Update local state
                if (!window.campaignColumnAggregations) {
                    window.campaignColumnAggregations = {};
                }
                window.campaignColumnAggregations[columnName] = newAggregation;

                // Store to localStorage
                localStorage.setItem(
                    'campaignColumnAggregations',
                    JSON.stringify(window.campaignColumnAggregations)
                );
            });
        });

        // ✅ Add event listeners for checkboxes to update selected count
        document.querySelectorAll('.value-checkbox').forEach((checkbox) => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        // ✅ Update selected count initially
        updateSelectedCount();

        // Show the modal using Bootstrap 5
        const modal = new bootstrap.Modal(
            document.getElementById('valueSelectionModal')
        );
        modal.show();
    });

    // Handle apply button click
    $('#apply-value-selection').on('click', function () {
        // ✅ Get all checked values using new class selector
        const selected = Array.from(
            document.querySelectorAll('.value-checkbox:checked')
        ).map((cb) => cb.value);

        valueSelectedState = selected;
        localStorage.setItem(
            'campaignValueSelectedState',
            JSON.stringify(valueSelectedState)
        );

        if (window.tiktokGmvMaxCampaignPivotTable && selected.length > 0) {
            // ✅ Update pivot table with current column aggregations
            window.tiktokGmvMaxCampaignPivotTable
                .updatePivotTableWithColumnAggregations()
                .catch((error) => {
                    // Handle error silently
                });
        }

        // Hide modal using Bootstrap 5
        const modal = bootstrap.Modal.getInstance(
            document.getElementById('valueSelectionModal')
        );
        if (modal) {
            modal.hide();
        }

        // Refresh pivot table
        setTimeout(() => {
            if (window.tiktokGmvMaxCampaignPivotTable) {
                window.tiktokGmvMaxCampaignPivotTable.showGrid();
            }
        }, 100);
    });
});

// ✅ Global helper functions for value selection modal
function getStoredColumnAggregations() {
    const stored = localStorage.getItem('campaignColumnAggregations');
    if (stored) {
        try {
            return JSON.parse(stored);
        } catch (e) {
            // Use defaults on parse error
        }
    }

    // Return default aggregations for campaigns
    return {
        Cost: 'Sum',
        GrossRevenue: 'Sum',
        Orders: 'Sum',
        ROI: 'Average',
        TACOS: 'Average',
        CostPerOrder: 'Average',
    };
}

function getValueDefsWithColumnAggregations(columnAggregations = null) {
    const aggregations = columnAggregations || getStoredColumnAggregations();

    const allValueDefs = [
        {
            name: 'GrossRevenue',
            caption: `Tổng doanh thu ($)`,
            type: aggregations.GrossRevenue || 'Sum',
            permission: 'ViewMetrics', // Revenue metrics
        },
        {
            name: 'Cost',
            caption: `Chi phí quảng cáo ($)`,
            type: aggregations.Cost || 'Sum',
            permission: 'ViewSpending', // Spending data
        },
        {
            name: 'ROAS',
            caption: `ROAS`,
            type: aggregations.ROAS || 'Average',
            permission: 'ViewMetrics', // Performance metrics
        },
        {
            name: 'TACOS',
            caption: `TACOS (%)`,
            type: aggregations.TACOS || 'Average',
            permission: 'ViewAll', // Restricted field - only for ViewAll/ViewAllAdvertisers
        },
        {
            name: 'Orders',
            caption: `Số đơn hàng`,
            type: aggregations.Orders || 'Sum',
            permission: 'ViewMetrics', // Order metrics
        },
        {
            name: 'CostPerOrder',
            caption: `Chi phí mỗi đơn ($)`,
            type: aggregations.CostPerOrder || 'Average',
            permission: 'ViewSpending', // Cost per order
        },
    ];

    // Filter based on permissions
    return allValueDefs.filter((valueDef) => {
        if (!window.PermissionHelper) {
            // Default: show all if PermissionHelper not available
            return true;
        }

        // Get permissions for campaign entity
        const permissions = window.PermissionHelper.getPermissions('campaign');

        // If user has ViewAll or ViewAllAdvertisers permission, show everything
        if (permissions.viewAll || permissions.viewAllAdvertisers) {
            return true;
        }

        // Check specific permission
        if (valueDef.permission === 'ViewSpending') {
            return permissions.viewSpending || permissions.viewAllAdvertisers;
        } else if (valueDef.permission === 'ViewMetrics') {
            return permissions.viewMetrics || permissions.viewAllAdvertisers;
        } else if (valueDef.permission === 'ViewAll') {
            return permissions.viewAll || permissions.viewAllAdvertisers;
        }

        // Default: hide if no matching permission
        return false;
    });
}

// ✅ Global function to ensure loading indicator is hidden
function ensureLoadingIndicatorHidden() {
    const indicator = document.getElementById('data-loading-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }

    // Remove ALL Bootstrap modal backdrops (there might be multiple)
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach((backdrop) => {
        backdrop.remove();
    });

    // Remove modal-open class from body and restore scroll
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // Also check html element
    document.documentElement.classList.remove('modal-open');
    document.documentElement.style.overflow = '';
}
