@page "~/fact-gmv-max-campaign"
@model TikTok.Web.Pages.FactGmvMaxCampaign.IndexModel
@{
    ViewData["Title"] = "Phân tích GMV Max Campaign TikTok";
}

@section Styles {
    <!-- ✅ Optimized: Preload critical CSS -->
    <link rel="preload" href="https://cdn.syncfusion.com/ej2/material.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://cdn.syncfusion.com/ej2/material.css" rel="stylesheet"></noscript>
    
    <link href="/css/dashboard-common.css" rel="stylesheet" />
    <link href="/css/fact-gmv-max-campaign.css" rel="stylesheet" />
    <link href="/css/toast.css" rel="stylesheet" />
    
    <!-- ✅ CSS moved to dashboard-common.css for consistency -->
}

@section Scripts {
    <script src="https://cdn.syncfusion.com/ej2/locale/vi.js" defer></script>
    <!-- Syncfusion Localization -->
    <script src="/js/syncfusion_localization.js"></script>
    <!-- ✅ Load Syncfusion immediately for pivot table -->
    <script src="https://cdn.syncfusion.com/ej2/dist/ej2.min.js"></script>

    <!-- Custom Scripts -->
    <script src="/js/shared/common-utils.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/shared/date-helper.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/shared/currency-manager.js?v=@DateTime.Now.Ticks"></script>
    <!-- ✅ Load permissionHelper.js FIRST before currency.js -->
    <script src="/js/permissionHelper.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/currency.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/factGmvMaxCampaign/utils/helper.js?v=@DateTime.Now.Ticks" defer></script>
    <script src="/js/factGmvMaxCampaign/utils/businessHelper.js?v=@DateTime.Now.Ticks" defer></script>
    <script src="/js/factGmvMaxCampaign/pivot/conditionalFormatting.js?v=@DateTime.Now.Ticks" defer></script>
    
    <!-- ✅ SIMPLE: Section-based Dashboard Scripts -->
    <script src="/js/factGmvMaxCampaign/section/summary.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/factGmvMaxCampaign/section/overview.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/factGmvMaxCampaign/section/charts.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/factGmvMaxCampaign/section/rankings.js?v=@DateTime.Now.Ticks"></script>
    <script src="/js/factGmvMaxCampaign/dashboard.js?v=@DateTime.Now.Ticks"></script>
    
    <!-- Main enhanced application script -->
    <script src="/js/factGmvMaxCampaign.js?v=@DateTime.Now.Ticks" defer></script>
    <script src="/js/factGmvMaxCampaignEventHandler.js?v=@DateTime.Now.Ticks" defer></script>
}

<div id="gmv-config"
     data-api-endpoint="@Url.Action("GetGmvMaxCampaignData", "FactGmvMaxCampaign")"
     data-refresh-interval="300000"
     data-locale="vi-VN"
     data-currency="USD"
     data-from-date="@((Model.FromDate ?? Model.From ?? DateTime.Now.AddDays(-7)).ToString("yyyy-MM-dd"))"
     data-to-date="@((Model.ToDate ?? Model.To ?? DateTime.Now).ToString("yyyy-MM-dd"))"
     data-type="@(Model.Type ?? "campaign")"
     data-auto-refresh="false"></div>

<div class="container-fluid">
    <!-- ✅ Enhanced Dashboard Header -->
    <div class="dashboard-header">
        <h1><i class="bi bi-tiktok"></i> Phân tích GMV Max Campaign</h1>
        <p>Phân tích thông minh hiệu suất chiến dịch GMV Max, ROI và tối ưu hóa doanh thu</p>
        
    </div>

    <!-- ✅ Dashboard Tổng hợp - NEW SECTION -->
    <div class="row mb-4 mt-4">
        <div class="col-12">
            <div id="dashboard-summary-cards-container">
                <!-- Loading state -->
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải dữ liệu tổng hợp...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ Tổng quan chi tiêu GMV Max - SEPARATE CONTAINER -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header dashboard-summary-header" data-bs-toggle="collapse" data-bs-target="#gmvMaxOverviewCollapse" aria-expanded="true">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Tổng quan chi tiêu GMV Max</h5>
                    </div>
                </div>
                <div class="collapse show" id="gmvMaxOverviewCollapse">
                    <div class="card-body">
                        <div id="overview-container">
                            <!-- Loading state -->
                            <div class="text-center p-4">
                                <div class="spinner-border text-success" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-muted">Đang tải dữ liệu tổng quan...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ Biểu đồ phân tích chi tiêu - Combined with Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header charts-header" data-bs-toggle="collapse" data-bs-target="#chartsCollapse" aria-expanded="true">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-chart-area"></i> Biểu đồ phân tích</h5>
                    </div>
                </div>
                <div class="collapse show" id="chartsCollapse">
                    <div class="card-body">
                        <!-- Tab Navigation -->
                        <ul class="nav nav-tabs mb-3" id="chartTabs" role="tablist">
                            @if (Model.HasViewSpending || Model.HasViewAll || Model.HasViewAllAdvertisers)
                            {
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="overview-chart-tab" data-bs-toggle="tab" data-bs-target="#overview-chart" type="button" role="tab" aria-controls="overview-chart" aria-selected="true">
                                        <i class="fas fa-chart-bar"></i> Tổng quan chi tiêu
                                    </button>
                                </li>
                            }
                            @if (Model.HasViewMetrics || Model.HasViewAll || Model.HasViewAllAdvertisers)
                            {
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link @(Model.HasViewSpending || Model.HasViewAll || Model.HasViewAllAdvertisers ? "" : "active")" id="detailed-chart-tab" data-bs-toggle="tab" data-bs-target="#detailed-chart" type="button" role="tab" aria-controls="detailed-chart" aria-selected="@(Model.HasViewSpending || Model.HasViewAll || Model.HasViewAllAdvertisers ? "false" : "true")">
                                        <i class="fas fa-chart-line"></i> Phân tích chi tiết
                                    </button>
                                </li>
                            }
                        </ul>
                        
                        <!-- Tab Content -->
                        <div class="tab-content" id="chartTabContent">
                            @if (Model.HasViewSpending || Model.HasViewAll || Model.HasViewAllAdvertisers)
                            {
                                <div class="tab-pane fade show active" id="overview-chart" role="tabpanel" aria-labelledby="overview-chart-tab">
                                    <div id="charts-container">
                                        <!-- Loading state -->
                                        <div class="text-center p-4">
                                            <div class="spinner-border text-info" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 text-muted">Đang tải biểu đồ...</p>
                                        </div>
                                    </div>
                                </div>
                            }
                            @if (Model.HasViewMetrics || Model.HasViewAll || Model.HasViewAllAdvertisers)
                            {
                                <div class="tab-pane fade @(Model.HasViewSpending || Model.HasViewAll || Model.HasViewAllAdvertisers ? "" : "show active")" id="detailed-chart" role="tabpanel" aria-labelledby="detailed-chart-tab">
                                    <div id="detailed-charts-container">
                                        <!-- Loading state -->
                                        <div class="text-center p-4">
                                            <div class="spinner-border text-warning" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 text-muted">Đang tải biểu đồ chi tiết...</p>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ Weekly Store Rankings Section - Combined with Tabs (Only visible with ViewAll permission) -->
    @if (Model.HasViewAll || Model.HasViewAllAdvertisers)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header ranking-section-header" data-bs-toggle="collapse" data-bs-target="#storeRankingsCollapse" aria-expanded="true">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-trophy"></i> Xếp hạng theo tuần</h5>
                            @* <i class="fas fa-chevron-down"></i> *@
                        </div>
                    </div>
                    <div class="collapse show" id="storeRankingsCollapse">
                        <div class="card-body">
                            <!-- Tab Navigation -->
                            <ul class="nav nav-tabs mb-3" id="rankingTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="cost-ranking-tab" data-bs-toggle="tab" data-bs-target="#cost-ranking" type="button" role="tab" aria-controls="cost-ranking" aria-selected="true">
                                        <i class="fas fa-chart-line"></i> Xếp hạng chi tiêu
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="revenue-ranking-tab" data-bs-toggle="tab" data-bs-target="#revenue-ranking" type="button" role="tab" aria-controls="revenue-ranking" aria-selected="false">
                                        <i class="fas fa-trophy"></i> Xếp hạng doanh thu
                                    </button>
                                </li>
                            </ul>
                            
                            <!-- Tab Content -->
                            <div class="tab-content" id="rankingTabContent">
                                <div class="tab-pane fade show active" id="cost-ranking" role="tabpanel" aria-labelledby="cost-ranking-tab">
                                    <div id="storeCostRankingsContainer" style="min-height: 300px; display: flex; justify-content: center; align-items: flex-start; padding-top: 50px;">
                                        <!-- Loading state -->
                                        <div class="text-center">
                                            <div class="spinner-border text-danger" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 text-muted">Đang tải xếp hạng chi tiêu...</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="revenue-ranking" role="tabpanel" aria-labelledby="revenue-ranking-tab">
                                    <div id="storeRankingsContainer" style="min-height: 300px; display: flex; justify-content: center; align-items: flex-start; padding-top: 50px;">
                                        <!-- Loading state -->
                                        <div class="text-center">
                                            <div class="spinner-border text-danger" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 text-muted">Đang tải xếp hạng doanh thu...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }


    <!-- ✅ Enhanced Pivot Table Container -->
    <div class="pivot-container">
        <div class="card">
            <div class="card-header pivot-table-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">
                            <i class="fas fa-table"></i> Bảng phân tích thông minh GMV Max Campaign
                        </h5>
                        <small>Sử dụng thanh công cụ và bộ lọc để tùy chỉnh phân tích</small>
                    </div>
                    <div class="col-auto">
                        <!-- ✅ NEW: Real-time status indicator -->
                        <span id="data-status-indicator" class="badge bg-success">
                            <i class="fas fa-check-circle"></i> Dữ liệu cập nhật
                        </span>
                    </div>
                </div>
            </div>

            <div class="card-body p-2">
                <!-- Main Actions Collapse -->
                <div class="collapse show" id="mainActionsCollapse">
                    <div class="mb-3 border-primary">
                        <h6 class="text-primary"><i class="fas fa-cogs"></i> Thao tác chính</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="btn-group me-2 mb-2" role="group">
                                    <button type="button" class="btn btn-primary" id="refresh-data">
                                        <i class="fas fa-sync-alt"></i> Làm mới
                                    </button>
                                    <button type="button" class="btn btn-info" id="toggle-chart">
                                        <i class="fas fa-chart-bar"></i> Biểu đồ
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" id="open-value-selection-modal">
                                        <i class="fas fa-sliders-h"></i> Cấu hình cột & Chỉ số
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 d-flex justify-content-end">
                                <div class="btn-group me-2 mb-2" role="group">
                                    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-download"></i> Xuất báo cáo
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="#" id="export-excel">
                                                <i class="fas fa-file-excel"></i> Excel (.xlsx)
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" id="export-pdf">
                                                <i class="fas fa-file-pdf"></i> PDF Report
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="btn-group me-2 mb-2" role="group">
                                    <button class="btn btn-outline-info btn-sm" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#filtersCollapse" aria-expanded="false" aria-controls="filtersCollapse">
                                        <i class="fas fa-filter"></i> Bộ lọc nâng cao
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Filtering Options Collapse -->
                <div class="collapse" id="filtersCollapse">
                    <div class="card card-body border-info">
                        <h6 class="text-info"><i class="fas fa-filter"></i> Bộ lọc nâng cao</h6>
                        
                        <!-- ✅ PERFORMANCE: Loading indicator for filters -->
                        <div id="filters-loading" class="text-center p-3" style="display: none;">
                            <div class="spinner-border spinner-border-sm text-info" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2 text-muted">Đang tải dữ liệu bộ lọc...</span>
                        </div>
                        
                        <!-- Hàng 1: Lọc khoảng thời gian, Lọc nhanh + Tìm kiếm text -->
                        <div class="row mb-3">
                            <!-- Date Range with Syncfusion DateRangePicker -->
                            <div class="col-md-4">
                                <label class="form-label fw-bold"><i class="fas fa-calendar-alt"></i> Khoảng thời gian:</label>
                                <input type="text" id="date-range-picker" />
                            </div>
                            
                            <!-- Quick Date Filter with Syncfusion DropDownList -->
                            <div class="col-md-2">
                                <label class="form-label fw-bold"><i class="fas fa-clock"></i> Lọc nhanh:</label>
                                <input type="text" id="quick-date-dropdown" />
                            </div>

                            <!-- Search Box -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold"><i class="fas fa-search"></i> Tìm kiếm:</label>
                                <input type="text" class="form-control form-control-sm" 
                                       id="keyword-search" 
                                       placeholder="Tên trung tâm, shop, chiến dịch...">
                            </div>
                        </div>

                        <!-- Hàng 2: MultiSelect Business Center và Shop -->
                        <div class="row mb-3">
                            <!-- Business Center Filter with Syncfusion MultiSelect -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold"><i class="fas fa-building"></i> Trung tâm kinh doanh:</label>
                                <input type="text" id="business-center-multiselect" />
                            </div>

                            <!-- Shop Filter with Syncfusion MultiSelect -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold"><i class="fas fa-store"></i> Shop:</label>
                                <input type="text" id="shop-multiselect" />
                            </div>
                        </div>

                        <!-- Hàng 3: Loại chiến dịch và Hiệu suất -->
                        <div class="row mb-3">
                            <!-- Campaign Type Filter with Syncfusion MultiSelect -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold"><i class="fas fa-tags"></i> Loại chiến dịch:</label>
                                <input type="text" id="campaign-type-multiselect" />
                            </div>

                            <!-- Performance Filter with Syncfusion MultiSelect -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold"><i class="fas fa-chart-line"></i> Hiệu suất:</label>
                                <input type="text" id="performance-multiselect" />
                            </div>
                        </div>

                        <!-- Hàng 4: Filter Actions -->
                        <div class="row">
                            <div class="col-12 text-end">
                                <button class="btn btn-outline-secondary btn-sm me-2" id="clear-all-filters">
                                    <i class="fas fa-times"></i> Xóa bộ lọc
                                </button>
                                <button class="btn btn-success btn-sm" id="apply-all-filters">
                                    <i class="fas fa-check"></i> Áp dụng
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- ✅ THÊM: Loading indicator cho Pivot Table -->
                <div id="pivot-loading-indicator" class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải bảng phân tích...</p>
                </div>
                
                <!-- Element where the enhanced pivot table will be rendered -->
                <div id="FactGmvMaxCampaignPivotTable" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- ✅ Enhanced Footer -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="footer-info">
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <small>
                                    <i class="fas fa-clock text-primary"></i>
                                    Dữ liệu cập nhật lần cuối: <strong
                                        id="last-updated">@DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")</strong>
                                    @{
                                        var recordCount = 0;
                                        var fromDisplay = (Model.FromDate ?? Model.From ?? DateTime.Now.AddDays(-30)).ToString("dd/MM/yyyy");
                                        var toDisplay = (Model.ToDate ?? Model.To ?? DateTime.Now).ToString("dd/MM/yyyy");
                                        
                                        try 
                                        {
                                            if (Model.Data != null)
                                            {
                                                var dataObj = Model.Data as dynamic;
                                                if (dataObj?.FactGmvMaxCampaigns != null)
                                                {
                                                    recordCount = ((ICollection<object>)dataObj.FactGmvMaxCampaigns).Count;
                                                }
                                            }
                                        }
                                        catch { /* Ignore errors */ }
                                    }
                                    @if (recordCount > 0)
                                    {
                                        <text>
                                            | <i class="fas fa-info-circle text-warning"></i>
                                            Phân tích <strong>@recordCount</strong> bản ghi
                                            từ <strong>@fromDisplay</strong>
                                            đến <strong>@toDisplay</strong>
                                        </text>
                                    }
                                    else
                                    {
                                        <text>
                                            | <i class="fas fa-info-circle text-muted"></i>
                                            Khoảng thời gian: <strong>@fromDisplay</strong> - <strong>@toDisplay</strong>
                                        </text>
                                    }
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary btn-sm"
                                        id="auto-refresh-toggle">
                                        <i class="fas fa-sync-alt"></i> Auto-refresh: <span
                                            id="auto-refresh-status">OFF</span>
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" id="help-toggle">
                                        <i class="fas fa-question-circle"></i> Trợ giúp
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- ✅ Enhanced Toast notifications -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1100">
        <div id="success-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <i class="fas fa-check-circle me-2"></i>
                <strong class="me-auto">Thành công</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="success-message">
                Thao tác đã được thực hiện thành công!
            </div>
        </div>

        <div id="error-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-danger text-white">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong class="me-auto">Lỗi</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="error-message">
                Có lỗi xảy ra trong quá trình thực hiện!
            </div>
        </div>

        <div id="info-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-info text-white">
                <i class="fas fa-info-circle me-2"></i>
                <strong class="me-auto">Thông tin</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="info-message">
                Thông tin hệ thống
            </div>
        </div>

        <div id="warning-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-warning text-white">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong class="me-auto">Cảnh báo</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="warning-message">
                Cảnh báo hệ thống
            </div>
        </div>
    </div>

    <!-- ✅ NEW: Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="helpModalLabel">
                        <i class="fas fa-question-circle"></i> Hướng dẫn sử dụng GMV Max Campaign Dashboard
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>🎯 Mục đích Dashboard</h6>
                    <p>Dashboard này giúp bạn:</p>
                    <ul>
                        <li>Theo dõi hiệu suất GMV Max Campaign real-time</li>
                        <li>Phân tích ROI và TACOS của từng chiến dịch</li>
                        <li>So sánh hiệu quả giữa Product và Live campaigns</li>
                        <li>Đưa ra cảnh báo và đề xuất tối ưu hóa</li>
                    </ul>

                    <h6>📊 Cách sử dụng Pivot Table</h6>
                    <ul>
                        <li><strong>Kéo thả:</strong> Di chuyển các trường giữa Rows, Columns, Values</li>
                        <li><strong>Lọc:</strong> Click vào Field List hoặc sử dụng bộ lọc nâng cao</li>
                        <li><strong>Sắp xếp:</strong> Click vào header để sắp xếp</li>
                        <li><strong>Xuất dữ liệu:</strong> Sử dụng nút Export để tạo báo cáo</li>
                    </ul>

                    <h6>⚠️ Hiểu các cảnh báo</h6>
                    <ul>
                        <li><span class="badge bg-danger">Đỏ</span> ROI < 1.5 (khẩn cấp)</li>
                        <li><span class="badge bg-warning">Vàng</span> ROI 1.5-2.0 (cảnh báo)</li>
                        <li><span class="badge bg-success">Xanh</span> ROI > 3.0 (xuất sắc)</li>
                        <li><span class="badge bg-info">Xanh nhạt</span> TACOS > 30% (chi phí cao)</li>
                    </ul>

                    <h6>📈 Các chỉ số quan trọng</h6>
                    <ul>
                        <li><strong>ROI:</strong> Return on Investment - Tỷ lệ hoàn vốn đầu tư</li>
                        <li><strong>TACOS:</strong> True ACOS - Chi phí quảng cáo trên tổng doanh thu</li>
                        <li><strong>CPO:</strong> Cost Per Order - Chi phí mỗi đơn hàng</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Value Selection Modal -->
    <div class="modal fade" id="valueSelectionModal" tabindex="-1" aria-labelledby="valueSelectionModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="valueSelectionModalLabel">
                        <i class="fas fa-sliders-h"></i> Lựa chọn giá trị hiển thị
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row" id="value-checkbox-list">
                        <!-- Checkboxes will be rendered here by JS -->
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-end">
                            <button type="button" class="btn btn-primary" id="apply-value-selection">
                                <i class="fas fa-check"></i> Áp dụng lựa chọn
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Hủy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
