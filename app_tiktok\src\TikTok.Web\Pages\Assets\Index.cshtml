@page
@using Microsoft.Extensions.Localization
@using TikTok.Localization
@using TikTok.Web.Pages.Assets
@model IndexModel
@inject IStringLocalizer<TikTokResource> L
@{
    ViewData["Title"] = L["Menu:Assets"];
}

@section scripts {
    <abp-script src="/Pages/Assets/Index.js" />
}

<abp-card>
    <abp-card-header>
        <abp-row>
            <abp-column size-md="_6">
                <abp-card-title>@L["Menu:Assets"]</abp-card-title>
            </abp-column>
            <abp-column size-md="_6" class="text-end">
                <abp-button id="NewAssetButton" text="@L["NewAsset"].Value" icon="plus" button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body class="pt-2">
        <abp-row>
            <abp-column size-md="_12">
                <form id="SearchForm" autocomplete="off">
                    <!-- Basic Search -->
                    <div class="input-group mb-3">
                        <input class="form-control me-1" id="AssetIdFilter" name="AssetIdFilter" placeholder="@L["Asset:AssetId"]" />
                        <input class="form-control" id="AssetNameFilter" name="AssetNameFilter" placeholder="@L["Asset:AssetName"]" />
                        <abp-button button-type="Primary" type="submit" icon="search" />
                        <abp-button button-type="Secondary" type="button" id="ClearSearchBtn" icon="times" text="@L["Clear"]" />
                    </div>
                </form>

                <!-- Search Tags Panel -->
                <div id="SearchTagsPanel" class="mb-3" style="display: none;">
                    <div id="SearchTags" class="d-flex flex-wrap gap-2"></div>
                </div>
            </abp-column>
        </abp-row>

        <abp-table striped-rows="true" id="AssetsTable">
            <thead>
                <tr>
                    <th>@L["Asset:AssetId"]</th>
                    <th>@L["Asset:AssetName"]</th>
                    <th>@L["Asset:AssetType"]</th>
                    <th>@L["BcId"]</th>
                    <th>@L["Asset:OwnerBcName"]</th>
                    <th>@L["Actions"]</th>
                </tr>
            </thead>
        </abp-table>
    </abp-card-body>
</abp-card>